# 新福利页换量需求技术文档

需求背景：

旧版积分中心页面，有个名为“赚积分”的任务列表，我们称为换量任务。核心功能及交互流程如下：

1、请求服务端web-activity/task/taskRecords?tag=rn&aid={aid}接口，获取任务列表并展示，展示的字段从接口中获取。交互按钮的三个状态“去完成”、“领取”、“已领取”绑定了接口中的status和contextMap字段。

2、点击“去完成”时，会请求服务端接口web-activity/task/v2/genGuideLink获取跳转到第三方页面/APP所需参数，如果接口正确下发字段内容，那就可以正常执行点击跳转流程，跳转完成后，三方页面或者APP回通过传入的参数，回调给我们服务端，同步任务完成状态；如果接口没有正常下发参数，或者从taskRecords接口中能判断这是一个非RTA(realtime action)类型的任务，那么需要RN/客户端自己判断任务是否已完成，目前逻辑是点击成功跳转就算已完成。

3、非RTA类型的任务，在判断已完成后，需要调用openapi-feeds-stream-app/activity/complete/task来标记任务已完成。

4、每次重新回到积分中心页面，都需要刷新该页面的taskRecords接口，该接口的status字段将会更新页面任务的流转状态，同时更新列表。

5、点击“领取”状态按钮时，调用web-activity/task/v2/drawTaskAward接口领取奖励，完成后刷新taskRecords接口更新页面。

需求目标：

需要将换量任务需求迁移到“福利中心”的“每日任务”模块下。

1、数据来源，任务列表的数据来源与每日任务其他数据一样，同一个接口返回，需要扩展该接口的字段并整理接口改造的文档。

2、接口改造，“积分中心”的taskRecords、genGuideLink、openapi-feeds-stream-app/activity/complete/task、drawTaskAward接口都不再使用。需要在福利中心增加新的接口，接口功能一样。整理接口设计文档跟每日任务接口改造记录在一起。

3、“福利中心”的每日任务下发金币而不再下发积分，所以下发金币的接口可以复用其他每日任务的接口。

4、原来获取跳转参数的genGuideLink接口，在新版福利中心的每日任务中，可以与每日任务列表接口合并，即下发列表同时，在列表的item中下发该任务的跳转参数。

5、UI与其他每日任务保持一致，包括间距、字体大小、颜色等。

6、点击调起三方应用/页面的逻辑，可以复用“积分中心”原有函数，或者核心逻辑抽离出来重写一份(如果原始的逻辑耦合了接口调用)。

部分测试用例：

1、每日任务接口下发字段完整性，下发时应该有更多的换量任务，并且可以区分每日任务和换量任务，换量任务字段与“积分中心”的taskRecords字段对齐，并且增加了genGuideLink接口的字段。

2、点击去完成时，需要区分RTA类型任务与非RTA类型任务，非RTA任务需要手动调用任务完成接口，同步给服务端。

3、每次APP重新展示“福利中心”页面时，需要刷新每日任务列表接口，以更新页面任务展示状态。

4、点击“领取”时，调用金币奖励下发接口，调用成功后，需要刷新列表，更新页面任务展示状态。

5、允许建立临时python文件测试接口和模拟数据，但文件命名需要规范，要方便被识别和被删除。

6、允许建立单元测试功能，测试主要流程的测试用例，但文件命名需要规范，要方便被识别和被删除。

附带原始积分中心的任务列表图片：

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/1wvqrebjLX5zQnak/img/b73a6765-7149-42a6-b901-0de14b29f7f3.png)

附带新版福利页每日任务图片：

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/1wvqrebjLX5zQnak/img/ae28de0d-c269-4bd8-9418-81969c9b9339.png)