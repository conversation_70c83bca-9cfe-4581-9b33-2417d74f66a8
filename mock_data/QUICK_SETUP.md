# 🚀 <PERSON> 快速配置指南

## ⚡ 5分钟快速配置

### 1. <PERSON>基础设置 (1分钟)

1. **启动Charles**
2. **配置SSL代理**:
   - `Proxy` → `SSL Proxying Settings`
   - `Add`: `*.ximalaya.com:443`
3. **手机连接代理**:
   - 手机WiFi设置代理为电脑IP:8888
   - 安装Charles证书

### 2. 配置Mock规则 (3分钟)

#### 规则1: 每日任务列表
- **Tools** → **Map Local** → **Add**
- **Host**: `adse.ximalaya.com`
- **Path**: `/incentive/ting/welfare/queryDailyTask/*`
- **Local Path**: 选择 `daily_task_list.json`

#### 规则2: 任务完成接口
- **Tools** → **Map Local** → **Add**
- **Host**: `adse.ximalaya.com`
- **Path**: `/incentive/ting/welfare/completeExchangeTask/*`
- **Local Path**: 选择 `complete_task.json`

#### 规则3: 奖励发放接口
- **Tools** → **Map Local** → **Add**
- **Host**: `adse.ximalaya.com`
- **Path**: `/incentive/ting/welfare/rewardGoldCoin/*`
- **Local Path**: 选择 `reward_gold_coin.json`

### 3. 启动测试 (1分钟)

```bash
# 启动React Native项目
yarn start

# 扫码进入应用
# 导航到: 福利中心 → 每日任务
```

## 🎯 预期结果

### 任务列表显示
- ✅ 6个换量任务
- ✅ 不同状态按钮（去完成/领取/已领取）
- ✅ RTA任务显示倒计时

### 交互功能
- ✅ 点击任务正常跳转
- ✅ 任务状态正确更新
- ✅ 奖励领取成功

## 🔍 调试检查

### Charles日志验证
1. **Structure面板**: 查看 `adse.ximalaya.com` 请求
2. **Map Local标识**: 请求显示 🔗 图标
3. **Response内容**: 确认返回Mock数据

### 常见问题
- **请求未被拦截**: 检查Host和Path配置
- **SSL错误**: 确认证书安装和SSL代理配置
- **数据格式错误**: 检查JSON文件格式

## 📋 测试检查清单

- [ ] Charles代理连接正常
- [ ] SSL证书安装完成
- [ ] 3个Mock规则配置完成
- [ ] React Native项目启动成功
- [ ] 手机扫码进入应用
- [ ] 福利中心页面正常显示
- [ ] 每日任务显示6个换量任务
- [ ] 任务点击跳转正常
- [ ] 奖励领取功能正常

---

**配置完成后即可开始完整的换量任务功能测试！** 🎉
