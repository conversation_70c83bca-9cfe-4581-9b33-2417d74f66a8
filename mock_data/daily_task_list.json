{"ret": 0, "msg": "success", "data": {"success": true, "code": 200, "list": [{"type": "RED_PACKET_RAIN", "title": "拆红包领金币", "subTitle": "疯狂点击红包雨10s，手速即金币数！", "coins": 100, "calmSeconds": 0, "positionId": 307, "positionName": "incentive_welfare", "extMap": "{\"type\":\"RED_PACKET_RAIN\",\"taskId\":\"104\"}", "btnText": "去领取", "icon": "https://imagev2.xmcdn.com/storages/ba06-audiofreehighqps/4A/57/GKwRIJEMIHHKAAAR8gPGRwC3.png", "darkIcon": "https://imagev2.xmcdn.com/storages/151a-audiofreehighqps/95/24/GKwRIJIMIHIDAAAUjAPGRx-S.png", "maxAmount": 500}, {"type": "MARKET", "title": "逛商城赚金币", "subTitle": "滑动浏览商品30秒，立得金币", "coins": 100, "calmSeconds": 30, "positionId": 306, "positionName": "welfare_mall_task", "extMap": "{\"type\":\"MARKET\",\"taskId\":\"103\"}", "btnText": "去逛逛", "icon": "https://imagev2.xmcdn.com/storages/b90f-audiofreehighqps/C1/3C/GAqhp50MIHIBAAAPDAPGRx6h.png", "darkIcon": "https://imagev2.xmcdn.com/storages/cec9-audiofreehighqps/95/76/GAqh4zIMIHICAAAOzAPGRx8I.png", "maxAmount": 500}, {"positionId": 1003, "positionName": "exchange_task_novel", "title": "去看免费小说领现金", "subTitle": "新人送1元，看书即可提现100金币", "coins": 100, "extMap": "{\"taskId\":10003,\"rta\":false}", "calmSeconds": 0, "btnText": "去完成", "type": "EXCHANGE", "taskId": 10003, "status": 0, "contextMap": {"0": "去完成", "1": "领取", "2": "已领取"}, "guideLink": "https://m.ximalaya.com/gatekeeper/novel-task?xbSource=54792", "h5Link": "https://m.ximalaya.com/gatekeeper/novel-task?xbSource=54792", "schemaLink": "iting://open?msg_type=12&url=https://m.ximalaya.com/gatekeeper/novel-task?xbSource=54792", "rta": false, "icon": "https://imagev2.xmcdn.com/storages/b90f-audiofreehighqps/C1/3C/GAqhp50MIHIBAAAPDAPGRx6h.png", "darkIcon": "https://imagev2.xmcdn.com/storages/cec9-audiofreehighqps/95/76/GAqh4zIMIHICAAAOzAPGRx8I.png", "desc": "新人送1元，看书即可提现100金币", "maxAmount": 500}, {"positionId": 1004, "positionName": "exchange_task_dubbing", "title": "0元学配音 挑战兼职", "subTitle": "0基础可学 接单兼职 点击得200金币", "coins": 200, "extMap": "{\"taskType\":\"exchange\",\"taskId\":10004,\"rta\":true}", "calmSeconds": 0, "btnText": "去参与", "type": "EXCHANGE", "taskType": "exchange", "taskId": 10004, "status": 2, "contextMap": {"0": "去参与", "1": "领取", "2": "已完成"}, "guideLink": "https://m.ximalaya.com/gatekeeper/lms-sale-page?expGroupId=25&xbSource=54792", "h5Link": "https://m.ximalaya.com/gatekeeper/lms-sale-page?expGroupId=25&xbSource=54792", "schemaLink": "iting://open?msg_type=100&url=https://m.ximalaya.com/gatekeeper/lms-sale-page?expGroupId=25&xbSource=54792", "rta": true, "maxAmount": 500, "icon": "https://imagev2.xmcdn.com/storages/b90f-audiofreehighqps/C1/3C/GAqhp50MIHIBAAAPDAPGRx6h.png", "darkIcon": "https://imagev2.xmcdn.com/storages/cec9-audiofreehighqps/95/76/GAqh4zIMIHICAAAOzAPGRx8I.png", "desc": "0基础可学 无需露脸 接单兼职"}, {"positionId": 1005, "positionName": "exchange_task_game", "title": "玩游戏赚金币", "subTitle": "休闲小游戏，边玩边赚钱+150金币", "coins": 150, "extMap": "{\"taskType\":\"exchange\",\"taskId\":10005,\"rta\":true}", "calmSeconds": 0, "btnText": "领取", "type": "EXCHANGE", "taskType": "exchange", "taskId": 10005, "status": 1, "contextMap": {"0": "去游戏", "1": "领取", "2": "已领取"}, "guideLink": "iting://open?msg_type=365&gameId=1001", "h5Link": "https://pages.ximalaya.com/mkt/act/game-center", "schemaLink": "iting://open?msg_type=365&gameId=1001", "rta": true, "maxAmount": 500, "icon": "https://imagev2.xmcdn.com/storages/b90f-audiofreehighqps/C1/3C/GAqhp50MIHIBAAAPDAPGRx6h.png", "darkIcon": "https://imagev2.xmcdn.com/storages/cec9-audiofreehighqps/95/76/GAqh4zIMIHICAAAOzAPGRx8I.png", "desc": "休闲小游戏，边玩边赚钱"}, {"positionId": 1006, "positionName": "exchange_task_ecommerce", "title": "购物返金币", "subTitle": "精选好货，购物还能赚120金币", "coins": 120, "extMap": "{\"taskType\":\"exchange\",\"taskId\":10006,\"rta\":false}", "calmSeconds": 0, "btnText": "已领取", "type": "EXCHANGE", "taskType": "exchange", "taskId": 10006, "status": 0, "maxAmount": 500, "contextMap": {"0": "去购物", "1": "领取", "2": "已领取"}, "guideLink": "iting://open?msg_type=94&bundle=rn_benefits_conversion&goodsShelfName=购物&goodsShelfId=4", "h5Link": "https://pages.ximalaya.com/mkt/act/shopping-task", "schemaLink": "iting://open?msg_type=94&bundle=rn_benefits_conversion&goodsShelfName=购物&goodsShelfId=4", "rta": false, "icon": "https://imagev2.xmcdn.com/storages/b90f-audiofreehighqps/C1/3C/GAqhp50MIHIBAAAPDAPGRx6h.png", "darkIcon": "https://imagev2.xmcdn.com/storages/cec9-audiofreehighqps/95/76/GAqh4zIMIHICAAAOzAPGRx8I.png", "desc": "精选好货，购物还能赚金币"}]}}