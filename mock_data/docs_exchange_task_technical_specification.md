# 喜马拉雅APP换量任务技术规范文档

## 文档概述

本文档面向服务端接口开发人员、测试人员、产品经理，详细描述喜马拉雅APP与第三方APP之间的换量任务技术实现方案，包括业务流程、接口变动和埋点上报规范。

---

## 第一部分：喜马APP调起第三方

### 1. 业务流程

#### 1.1 任务展示流程
1. **任务列表获取**：APP调用 `queryDailyTask` 接口获取每日任务列表
2. **换量任务识别**：通过 `type: "EXCHANGE"` 字段识别换量任务
3. **UI渲染**：换量任务与普通每日任务使用统一UI组件，支持三种按钮状态
   - `status=0`：未完成 → 显示"去完成"按钮（红色）
   - `status=1`：已完成 → 显示"领取"按钮（绿色）
   - `status=2`：已领取 → 显示"已领取"按钮（灰色）

#### 1.2 用户交互流程

**状态0（未完成）- 去完成流程**：
1. **用户点击**：用户点击换量任务的"去完成"按钮
2. **埋点上报**：触发点击埋点（67695），action="去完成"
3. **跳转逻辑**：
   - 优先使用 `schemaLink` 拉起第三方APP
   - 失败则降级使用 `h5Link` 打开H5页面
4. **任务标记**：非RTA任务立即调用 `completeExchangeTask` 标记完成
5. **状态刷新**：iOS延迟刷新，Android立即刷新任务状态

**状态1（已完成）- 领取流程**：
1. **用户点击**：用户点击"领取"按钮
2. **埋点上报**：触发点击埋点（67695），action="领取"
3. **奖励发放**：调用 `rewardGoldCoin` 接口发放金币奖励
4. **状态更新**：任务状态更新为已领取（status=2）

**状态2（已领取）- 完成状态**：
1. **按钮状态**：显示"已领取"，按钮置灰不可点击
2. **无交互**：用户无法再次操作此任务

#### 1.3 上报流程
- **曝光上报**：任务出现在可视区域时触发埋点（67696）
- **点击上报**：用户点击时触发埋点（67695），包含action字段

### 2. 接口变动

#### 2.1 改造接口

**每日任务列表接口**
```
GET /incentive/ting/welfare/queryDailyTask/ts-{timestamp}
```

**响应数据扩展**：
```json
{
  "ret": 0,
  "msg": "success", 
  "data": {
    "list": [
      {
        "positionId": 1001,
        "positionName": "exchange_task_red_packet",
        "title": "折红包领金币",
        "subTitle": "疯狂点击红包10s，手速即金速！",
        "coins": 100,
        "extMap": "{\"taskId\":10001,\"rta\":false}",
        "calmSeconds": 0,
        "btnText": "去完成",
        "type": "EXCHANGE",
        "taskId": 10001,
        "status": 0,
        "contextMap": {
          "0": "去完成",
          "1": "领取",
          "2": "已领取"
        },
        "h5Link": "https://pages.ximalaya.com/mkt/act/red-packet-task",
        "schemaLink": "iting://open?msg_type=94&bundle=rn_benefits_conversion&goodsShelfName=折红包&goodsShelfId=1",
        "rta": false,
        "logo": "https://fdfs.xmcdn.com/storages/8698-audiofreehighqps/AF/E5/CMCoOSMEVwY-AAADfwChqChq.png",
        "desc": "疯狂点击红包10s，手速即金速！",
        "icon": null,
        "darkIcon": null,
        "maxAmount": 0
      }
    ]
  }
}
```

#### 2.2 新增接口

**换量任务完成标记接口**
```
POST /incentive/ting/welfare/completeExchangeTask/ts-{timestamp}
Content-Type: application/json

{
  "taskId": 10003,
  "aid": 0
}
```

**响应**：
```json
{
  "ret": 0,
  "msg": "success",
  "data": {
    "success": true
  }
}
```

### 3. 埋点上报

#### 3.1 统一埋点字段
所有任务（普通+换量）使用统一字段结构：

**曝光埋点（67696）**：
```json
{
  "currPage": "welfareCenter",
  "moduleTitle": "每日任务",
  "taskTitle": "去看免费小说领现金",
  "taskId": "10003",              // 换量任务使用taskId，普通任务使用positionId
  "taskType": "exchange",         // 换量任务为"exchange"，普通任务为具体类型
  "positionNew": "3",            // 列表中的真实位置
  "Item": "去完成"               // 按钮文案
}
```

**点击埋点（67695）**：
```json
{
  "currPage": "welfareCenter", 
  "moduleTitle": "每日任务",
  "taskTitle": "去看免费小说领现金",
  "taskId": "10003",
  "taskType": "exchange", 
  "positionNew": "3",
  "Item": "去完成",
  "action": "去完成"             // 换量任务专用字段
}
```



---

## 第二部分：第三方调起喜马APP

### 1. 业务流程

#### 1.1 独立的数据上报流程
**重要说明**：此流程与福利中心每日任务完全独立，不会影响任务状态。

1. **任务完成**：用户在第三方APP完成指定任务
2. **Deeplink拉起**：第三方APP通过约定的deeplink格式拉起喜马拉雅APP福利中心落地页
3. **参数解析**：喜马拉雅APP解析deeplink中的换量任务参数
4. **数据上报**：调用专用接口 `finishExchangeTask` 向服务端上报任务完成数据
5. **流程结束**：上报完成后流程结束，不涉及UI更新或状态同步

#### 1.2 Deeplink格式
```
iting://open?msg_type=94&bundle=rn_credit_center&cid=200183&channelid=keepchange&channelName=keepchange&token=aabbccddeeffaaddaadd&is_growth_exchange=true
```

**参数说明**：
- `msg_type`: 消息类型，**固定为"94"**（外部跳转喜马拉雅福利页专用）
- `bundle`: 固定为"rn_credit_center"（APP代码中处理）
- `cid`: 渠道ID（数字）
- `channelid`: 渠道标识（字符串）
- `channelName`: 渠道名称
- `token`: 任务令牌
- `is_growth_exchange`: 增长换量标识，固定为"true"



### 2. 接口变动

#### 2.1 新增接口

**换量任务完成上报接口**
```
POST /incentive/ting/welfare/finishThirdpartyTask/exchangeTask/ts-{timestamp}
Content-Type: application/json

{
  "channelId": 200183,
  "token": "__TOKEN__",
  "channelName": "keepchange", 
  "msgType": "94",
  "isGrowthExchange": true,
  "deeplinkData": "aXRpbmc6Ly9vcGVuP21zZ190eXBlPTE0JmNpZD0yMDAxODMmY2hhbm5lbGlkPWtlZXBjaGFuZ2UmY2hhbm5lbE5hbWU9a2VlcGNoYW5nZSZ0b2tlbj1fX1RPS0VOX18mdGFzaz0xJmlzX2dyb3d0aF9leGNoYW5nZT10cnVlJnVybD1odHRwcyUzQSUyRiUyRm0ueGltYWxheWEuY29tJTJGZ2F0ZWtlZXBlciUyRmdyb3d0aC1kb3dubG9hZC1wYWdlLWZyb250JTJGBWRK"
}
```

**响应**：
```json
{
  "ret": 0,
  "msg": "success",
  "data": {
    "taskId": "10003",
    "channelName": "keepchange",
    "channelId": 200183,
    "reward": 100,
    "status": "completed",
    "reportTime": 1703123456789
  }
}
```

**注意事项**：
- `deeplinkData` 字段为完整deeplink的base64编码
- 前端只关注 `ret` 和 `msg` 字段，其他字段仅用于日志
- 不显示toast提示，避免用户感知
- **此接口与福利中心每日任务无关**，仅用于数据统计

---

## 接口调用时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant XimalayaApp as 喜马拉雅APP
    participant ThirdPartyApp as 第三方APP
    participant Server as 服务端

    Note over User,Server: 流程一：喜马调起第三方（福利中心每日任务）
    User->>XimalayaApp: 打开福利中心
    XimalayaApp->>Server: queryDailyTask (获取任务列表)
    Server-->>XimalayaApp: 返回包含换量任务的列表

    User->>XimalayaApp: 点击换量任务"去完成"
    XimalayaApp->>XimalayaApp: 埋点上报(67695, action="去完成")
    XimalayaApp->>ThirdPartyApp: schemaLink/h5Link跳转
    XimalayaApp->>Server: completeExchangeTask (非RTA任务标记完成)
    Server-->>XimalayaApp: 任务状态更新为已完成(status=1)

    User->>XimalayaApp: 点击"领取"按钮
    XimalayaApp->>XimalayaApp: 埋点上报(67695, action="领取")
    XimalayaApp->>Server: rewardGoldCoin (发放金币奖励)
    Server-->>XimalayaApp: 奖励发放成功，状态更新为已领取(status=2)

    Note over User,Server: 流程二：第三方调起喜马（独立数据上报）
    User->>ThirdPartyApp: 在第三方APP完成任务
    ThirdPartyApp->>XimalayaApp: deeplink拉起喜马拉雅福利中心

    XimalayaApp->>Server: finishExchangeTask (独立数据上报)
    Server-->>XimalayaApp: 确认上报成功
    Note over XimalayaApp: 流程结束，不影响福利中心任务状态
```

---

## 测试要点

### 功能测试

**福利中心每日任务测试**：
1. **任务展示**：验证换量任务在每日任务列表中正确显示
2. **三种状态交互**：
   - status=0：验证"去完成"按钮跳转功能
   - status=1：验证"领取"按钮奖励发放
   - status=2：验证"已领取"按钮置灰状态
3. **状态流转**：验证任务状态从未完成→已完成→已领取的正确流转
4. **奖励发放**：验证金币奖励正确发放到用户账户

**第三方拉起测试**：
1. **独立流程**：验证第三方deeplink拉起不影响福利中心任务状态
2. **数据上报**：验证finishExchangeTask接口正常上报数据

### 接口测试
1. **queryDailyTask**：验证返回数据包含换量任务必要字段
2. **completeExchangeTask**：验证任务完成标记接口正常工作
3. **finishExchangeTask**：验证第三方拉起后的独立数据上报接口
4. **rewardGoldCoin**：验证金币奖励发放接口

### 埋点测试
1. **字段一致性**：验证换量任务和普通任务埋点字段统一
2. **数据准确性**：验证taskId、taskType、positionNew字段正确
3. **action字段**：验证换量任务点击时action字段正确（"去完成"/"领取"）

---

## 技术实现要点

### RTA vs 非RTA任务区分
- **RTA任务**：Real Time Attribution，第三方实时归因，无需手动标记完成
- **非RTA任务**：需要客户端调用 `completeExchangeTask` 手动标记完成

### 平台差异处理
- **iOS**：应用切换后需要延迟刷新任务状态
- **Android**：可以立即刷新任务状态

### 错误处理
1. **跳转失败**：schema失败时自动降级到h5链接
2. **接口异常**：上报失败不影响用户体验，仅记录日志
3. **参数缺失**：deeplink参数不完整时跳过处理

### Deeplink处理策略
- **优先使用原始deeplink**：避免参数丢失或顺序问题
- **备用构建方案**：当没有原始deeplink时，按标准格式重新构建
- **参数验证**：确保构建的deeplink包含必要的msg_type和is_growth_exchange参数
- **安全检查**：传递给服务端前验证deeplink不为空

### 数据安全
- 所有敏感参数通过base64编码传输
- 接口调用包含时间戳防重放
- 错误信息不暴露内部实现细节

---

## 附录：完整接口规范

### A. 金币奖励接口（复用现有）
```
POST /incentive/ting/welfare/rewardGoldCoin/ts-{timestamp}
Content-Type: application/json

{
  "requestId": "uuid-generated-string",
  "ts": 1703123456789,
  "coins": 100,
  "sourceName": "DAILY_TASK",
  "rewardType": 6,
  "ticket": "xuid-generated-ticket",
  "signature": "md5-generated-signature",
  "retry": 0,
  "fallbackReq": 0,
  "extMap": "{\"taskType\":\"exchange\",\"taskId\":10005,\"rta\":true}",
  "taskId": 10005
}
```

### B. 数据结构定义
```typescript
// 换量任务状态枚举
enum ExchangeTaskStatus {
  UNFINISHED = 0,  // 未完成
  FINISHED = 1,    // 已完成
  CLAIMED = 2      // 已领取
}

// 任务数据结构
interface DailyTaskItem {
  positionId: number;
  title: string;
  type: "EXCHANGE" | "RED_PACKET_RAIN" | "MARKET";
  taskType?: string;
  taskId?: number;
  status?: ExchangeTaskStatus;
  contextMap?: Record<string, string>;
  h5Link?: string;
  schemaLink?: string;
  rta?: boolean;
  coins: number;
  subTitle: string;
}
```
