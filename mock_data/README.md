# Charles Mock 数据配置指南

## 📋 Mock接口列表

本文件夹包含了换量任务功能测试所需的3个核心接口的Mock数据。

### 1. 每日任务列表接口

**文件**: `daily_task_list.json`
**接口路径**: `/incentive/ting/welfare/queryDailyTask/ts-*`
**请求方式**: GET
**说明**: 返回包含6个换量任务的每日任务列表，包含RTA和非RTA任务

**URL参数**:
- `drinkwater=1`: 支持喝水任务
- `exchange=1`: 支持换量任务（新增参数，用于版本控制）

### 2. 任务完成状态同步接口

**文件**: `complete_task.json`
**接口路径**: `/incentive/ting/welfare/completeExchangeTask/ts-*`
**请求方式**: POST
**说明**: 非RTA任务完成后，客户端调用此接口同步任务状态

### 3. 金币奖励发放接口

**文件**: `reward_gold_coin.json`
**接口路径**: `/incentive/ting/welfare/rewardGoldCoin/ts-*`
**请求方式**: POST
**说明**: 任务处于"领取"状态时，点击领取按钮调用此接口

## 🔧 Charles配置步骤

### 1. 打开Charles Map Local

1. 启动Charles
2. 菜单栏选择 **Tools** → **Map Local**
3. 点击 **Add** 添加新规则

### 2. 配置每日任务列表接口

- **Protocol**: https
- **Host**: adse.ximalaya.com (或 adse.test.ximalaya.com)
- **Port**: 443
- **Path**: `/incentive/ting/welfare/queryDailyTask/*`
- **Local Path**: 选择 `daily_task_list.json` 文件

### 3. 配置任务完成接口

- **Protocol**: https
- **Host**: adse.ximalaya.com
- **Port**: 443
- **Path**: `/incentive/ting/welfare/completeExchangeTask/*`
- **Local Path**: 选择 `complete_task.json` 文件

### 4. 配置奖励发放接口

- **Protocol**: https
- **Host**: adse.ximalaya.com
- **Port**: 443
- **Path**: `/incentive/ting/welfare/rewardGoldCoin/*`
- **Local Path**: 选择 `reward_gold_coin.json` 文件

### 5. 启用SSL代理

1. 菜单栏选择 **Proxy** → **SSL Proxying Settings**
2. 点击 **Add**
3. 添加 `*.ximalaya.com:443`

## 📱 测试流程

### 1. 环境准备
1. 启动Charles并配置上述Mock规则
2. 手机连接Charles代理
3. 启动React Native项目: `yarn start`

### 2. 功能测试

#### 第一步：验证任务列表显示
1. **扫码进入应用**
2. **导航到福利中心 → 每日任务**
3. **验证任务列表**: 应显示6个换量任务
   - 折红包领金币 (非RTA, 去完成)
   - 逛商城赚金币 (RTA, 去逛逛)
   - 去看免费小说领现金 (非RTA, 去完成)
   - 0元学配音 挑战兼职 (RTA, 去参与)
   - 玩游戏赚金币 (RTA, 领取) - 绿色按钮
   - 购物返金币 (非RTA, 已领取) - 灰色按钮

#### 第二步：测试非RTA任务
1. **点击"折红包领金币"任务**
2. **跳转到第三方应用**
3. **返回喜马拉雅应用**
4. **验证**: 任务状态应从"去完成"变为"领取"

#### 第三步：测试RTA任务
1. **点击"逛商城赚金币"任务**
2. **跳转到商城页面**
3. **等待30秒倒计时**
4. **返回福利中心**
5. **验证**: 任务状态应从"去逛逛"变为"领取"

#### 第四步：测试奖励领取
1. **点击"玩游戏赚金币"的"领取"按钮**
2. **验证**: 显示"恭喜获得150金币奖励！"
3. **验证**: 按钮状态变为"已领取"（灰色）

### 3. 验证要点
- ✅ 任务列表正确显示6个换量任务
- ✅ RTA任务显示倒计时（30秒）
- ✅ 非RTA任务点击后状态正确更新
- ✅ 奖励领取功能正常，显示金币数量
- ✅ 按钮状态和颜色正确切换
- ✅ Charles日志显示API请求被正确拦截

## 🎯 任务状态说明

- **status: 0** - "去完成"状态，显示对应按钮文本
- **status: 1** - "领取"状态，显示"领取"按钮
- **status: 2** - "已领取"状态，显示"已领取"按钮（置灰）

## 💡 调试技巧

1. **查看Charles日志**: 确认请求被正确拦截
2. **检查响应内容**: 验证Mock数据格式正确
3. **逐步测试**: 先测试列表显示，再测试交互功能
4. **网络监控**: 重点关注API请求的时序和参数

## 🔗 Deeplink测试

### 测试外部APP拉起功能
1. **模拟外部deeplink调起**:
   ```javascript
   // 在控制台执行测试
   import('./utils/exchangeTaskHelper').then(({ testDeeplinkBuilding }) => {
     testDeeplinkBuilding({
       msg_type: '94',
       bundle: 'rn_credit_center',
       cid: '200183',
       channelid: 'keepchange',
       channelName: 'keepchange',
       token: 'test_token_123',
       is_growth_exchange: 'true'
     });
   });
   ```

2. **验证要点**:
   - ✅ 参数正确解析
   - ✅ 支持手动拼接deeplink
   - ✅ iting://链接正确构建
   - ✅ Base64编码/解码一致性
   - ✅ finishExchangeTask接口deeplinkData字段不为空

3. **ADB命令测试**:
   ```bash
   adb shell am start -W -a android.intent.action.VIEW -d 'iting://open?msg_type=94\&bundle=rn_credit_center\&is_growth_exchange=true\&cid=0000011111\&channelName=zhifubao\&token=abcabc\&srcChannel=outter_zhifubao'
   ```



---

**注意**: 测试完成后记得关闭Charles代理，恢复正常网络环境。
