# 换量任务API接口文档

## 概述

本文档描述了福利中心换量任务功能所需的4个核心API接口规范，供服务端开发使用。

## 接口列表

1. **查询每日任务列表** - `queryDailyTask`
2. **完成换量任务同步** - `completeExchangeTask`
3. **发放金币奖励** - `rewardGoldCoin`
4. **第三方任务完成上报** - `finishThirdpartyTask` (积分中心使用)
5. **换量任务完成上报** - `finishExchangeTask` (福利中心专用，新接口)

---

## 1. 查询每日任务列表 (queryDailyTask)

**接口名称**: 查询每日任务列表
**接口地址**: `GET /incentive/ting/welfare/queryDailyTask/ts-{timestamp}?drinkwater=1&exchange=1`
**请求方式**: GET
**Content-Type**: application/json

**URL参数说明**:
- `drinkwater=1`: 表示客户端支持喝水任务
- `exchange=1`: 表示客户端支持换量任务（用于版本控制，服务端可根据此参数决定是否下发换量任务）

**请求参数**: 无

**响应格式**:
```json
{
  "ret": 0,
  "msg": "success",
  "data": {
    "success": true,
    "code": 200,
    "list": [
      {
        "positionId": 1001,
        "positionName": "exchange_task_red_packet",
        "title": "折红包领金币",
        "subTitle": "疯狂点击红包10s，手速即金速！",
        "coins": 100,
        "extMap": "{\"taskId\":10001,\"rta\":false}",
        "calmSeconds": 0,
        "btnText": "去完成",
        "type": "EXCHANGE",
        "taskId": 10001,
        "status": 0,
        "contextMap": {
          "0": "去完成",
          "1": "领取",
          "2": "已领取"
        },
        "h5Link": "https://pages.ximalaya.com/mkt/act/red-packet-task",
        "schemaLink": "iting://open?msg_type=94&bundle=rn_benefits_conversion&goodsShelfName=折红包&goodsShelfId=1",
        "rta": false,
        "logo": "https://fdfs.xmcdn.com/storages/8698-audiofreehighqps/AF/E5/CMCoOSMEVwY-AAADfwChqChq.png",
        "desc": "疯狂点击红包10s，手速即金速！",
        "icon": null,
        "darkIcon": null,
        "maxAmount": 0
      }
    ]
  }
}
```

**换量任务扩展字段说明**:
- `type`: 任务类型，换量任务为 `"EXCHANGE"`
- `taskId`: 换量任务ID (直接字段)
- `status`: 换量任务状态 (0-未完成 1-已完成 2-已领取)
- `contextMap`: 按钮文案映射 `{"0":"去完成","1":"领取","2":"已领取"}`
- `h5Link`: **H5跳转链接** - 直接从每日任务接口获取，用作备用方案
- `schemaLink`: **Schema跳转链接** - 直接从每日任务接口获取，优先使用
- `rta`: 是否为RTA类型任务
- `logo`: 任务图标
- `desc`: 任务描述
- `extMap`: JSON字符串，包含换量任务扩展信息

**跳转链接使用逻辑** (福利中心):
1. **直接使用**: `h5Link` 和 `schemaLink` 已在每日任务接口中直接下发
2. **跳转优先级**:
   - 优先尝试 `schemaLink` (拉起第三方APP)
   - 失败则使用 `h5Link` (打开H5页面)
3. **无需额外接口**: 不需要调用 `guideLink` 接口

---

## 2. 完成换量任务同步 (completeExchangeTask)

**接口名称**: 完成换量任务同步
**接口地址**: `POST /incentive/ting/welfare/completeExchangeTask/ts-{timestamp}`
**请求方式**: POST
**Content-Type**: application/json

**功能**: 标记非RTA换量任务为已完成

**请求参数**:
```json
{
  "taskId": 10001,
  "aid": 0
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `taskId` | `number` | ✅ | 换量任务ID |
| `aid` | `number` | ❌ | 活动ID（可选，默认0） |

**注意**: `taskId` 字段是必填的，用于标识具体的换量任务

**响应格式**:
```json
{
  "success": true,
  "code": 200,
  "message": "任务完成标记成功",
  "data": {
    "taskId": 10001,
    "status": 1,
    "completedAt": "2025-06-10T20:30:00Z",
    "nextStatus": "可领取奖励"
  }
}
```

**响应字段说明**:
- `success`: 操作是否成功
- `code`: 状态码
- `message`: 操作结果消息
- `taskId`: 任务ID
- `status`: 任务状态 (1表示已完成)
- `completedAt`: 完成时间
- `nextStatus`: 下一步状态描述

---

## 3. 发放金币奖励 (rewardGoldCoin)

**接口名称**: 发放金币奖励
**接口地址**: `POST /incentive/ting/welfare/rewardGoldCoin/ts-{timestamp}`
**请求方式**: POST
**Content-Type**: application/json

**功能**: 发放换量任务金币奖励（复用现有接口）

**请求参数**:
```json
{
  "rewardType": 6,
  "sourceName": "DAILY_TASK",
  "coins": 100,
  "extMap": "{\"taskId\":10001,\"taskType\":\"exchange\"}",
  "fallbackReq": 0,
  "taskId": 10001
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `rewardType` | `number` | ✅ | 奖励类型，使用 `6` (RewardType.DAILY) |
| `sourceName` | `string` | ✅ | 来源名称，使用 `"DAILY_TASK"` (AD_SOURCE.DAILY) |
| `coins` | `number` | ✅ | 金币数量 |
| `extMap` | `string` | ❌ | 扩展信息，包含换量任务标识 |
| `fallbackReq` | `number` | ✅ | 发放方式，使用 `0` (FallbackReqType.NORMAL) |
| `taskId` | `number` | ✅ | 【换量任务功能】任务ID，用于换量任务奖励发放 |

**为什么使用"DAILY"类型**:
1. **业务归类**: 换量任务属于每日任务模块的一部分，与红包雨、商城任务等同级
2. **统一管理**: 使用相同的奖励类型便于后台统计和管理每日任务相关的金币发放
3. **复用逻辑**: 复用现有的每日任务奖励发放逻辑，减少开发成本
4. **数据统计**: 便于在数据分析中将换量任务归类到每日任务板块进行统计

**枚举值对应**:
- `RewardType.DAILY = 6` - 日常任务奖励类型
- `AD_SOURCE.DAILY = 'DAILY_TASK'` - 日常任务来源标识
- `FallbackReqType.NORMAL = 0` - 正常发放（非兜底）

**响应格式**:
```json
{
  "responseId": 19084788657608,
  "ret": 0,
  "data": {
    "toast": "恭喜获得100金币",
    "retry": false,
    "coins": 100,
    "balance": 912,
    "upgradeCoins": 500,
    "adAgainCoins": 452,
    "success": true,
    "code": 200,
    "msg": null
  }
}
```

**响应字段说明**:
- `responseId`: 响应ID
- `ret`: 返回码 (0表示成功)
- `toast`: 提示文案
- `retry`: 是否需要重试
- `coins`: 发放的金币数量
- `balance`: 用户当前金币余额
- `upgradeCoins`: 升级所需金币
- `adAgainCoins`: 广告再次获得金币
- `success`: 操作是否成功
- `code`: 状态码
- `msg`: 消息

---

## 4. 第三方任务完成上报 (finishThirdpartyTask)

**接口名称**: 第三方任务完成上报  
**接口地址**: `POST https://api.ximalaya.com/openapi-feeds-stream-app/activity/complete/task`  
**请求方式**: POST  
**Content-Type**: application/json

**使用场景**: 当用户从第三方APP（如快手）通过deeplink拉起喜马拉雅APP时，需要向服务端上报任务完成状态。

**重要说明**:
- 福利中心换量任务使用**简化版本**，不需要nonce和sig验证
- 积分中心第三方任务使用**完整版本**，需要nonce和sig验证
- 两种场景使用相同的接口，但参数要求不同

**请求参数**:

**福利中心换量任务（简化版）**:
```json
{
  "channelId": 200183,
  "token": "aabbccddeeffaaddaadd",
  "channelName": "keepchange",
  "nonce": "",
  "timestamp": 1699123456789,
  "sig": "",
  "msgType": "94",
  "isGrowthExchange": true,
  "channelid": "keepchange"
}
```
**映射关系**:
msg_type=94                    → msgType: "94"
bundle=rn_credit_center        → (APP代码处理，不传递给接口)
cid=200183                     → channelId: 200183
channelid=keepchange           → channelid: "keepchange"
channelName=keepchange         → channelName: "keepchange"
token=aabbccddeeffaaddaadd      → token: "aabbccddeeffaaddaadd"
is_growth_exchange=true        → isGrowthExchange: true

**积分中心第三方任务（完整版）**:
```json
{
  "channelId": 1001,
  "task": "10001",
  "token": "abc123token",
  "channelName": "kuaishou",
  "nonce": "uuid_generated_string",
  "timestamp": 1699123456789,
  "sig": "generated_signature_hash"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 | 获取方式 | 福利中心 | 积分中心 |
|--------|------|------|------|----------|----------|----------|
| `channelId` | `number` | ✅ | 渠道ID | 从deeplink的`cid`参数获取 | ✅ | ✅ |
| `token` | `string` | ✅ | 任务令牌 | 从deeplink的`token`参数获取 | ✅ | ✅ |
| `channelName` | `string` | ✅ | 渠道名称 | 从deeplink的`channelName`参数获取 | ✅ | ✅ |
| `nonce` | `string` | ❌/✅ | 随机数(防重放) | UUID生成 | 空字符串 | 必须生成 |
| `timestamp` | `number` | ✅ | 时间戳 | 当前时间戳 | ✅ | ✅ |
| `sig` | `string` | ❌/✅ | 签名验证 | 根据参数生成 | 空字符串 | 必须生成 |
| `msgType` | `string` | ❌ | 消息类型 | 从deeplink的`msg_type`参数获取 | ✅ | ❌ |
| `isGrowthExchange` | `boolean` | ❌ | 增长换量标识 | 从deeplink的`is_growth_exchange`参数获取 | ✅ | ❌ |
| `channelid` | `string` | ❌ | 渠道标识 | 从deeplink的`channelid`参数获取 | ✅ | ❌ |

**业务场景区分**:
1. **福利中心换量任务**: 包含完整的deeplink参数，nonce和sig传空字符串
2. **积分中心第三方任务**: 使用核心参数，需要生成nonce和sig

**响应格式**:

**福利中心换量任务响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "taskId": "1",
    "channelName": "keepchange",
    "channelId": 200183,
    "reward": 200,
    "status": "completed",
    "reportTime": 1699123456789,
    "token": "test_token",
    "msgType": "94",
    "isGrowthExchange": true
  }
}
```

**积分中心第三方任务响应**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "taskId": "10001",
    "channelName": "kuaishou",
    "channelId": 1001,
    "reward": 200,
    "status": "completed",
    "reportTime": 1699123456789,
    "schema": "kuaishou://...",
    "url": "https://..."
  }
}
```

**响应字段说明**:
| 字段名 | 类型 | 说明 | 福利中心 | 积分中心 |
|--------|------|------|----------|----------|
| `code` | `number` | 状态码 | ✅ | ✅ |
| `msg` | `string` | 响应消息 | ✅ | ✅ |
| `taskId` | `string` | 任务ID | ✅ | ✅ |
| `channelName` | `string` | 渠道名称 | ✅ | ✅ |
| `channelId` | `number` | 渠道ID | ✅ | ✅ |
| `reward` | `number` | 奖励金币数量 | ✅ | ✅ |
| `status` | `string` | 任务状态 | ✅ | ✅ |
| `reportTime` | `number` | 上报时间戳 | ✅ | ✅ |
| `token` | `string` | 任务令牌 | ✅ | ❌ |
| `msgType` | `string` | 消息类型 | ✅ | ❌ |
| `isGrowthExchange` | `boolean` | 是否增长换量 | ✅ | ❌ |
| `schema` | `string` | 跳转Schema | ❌ | ✅ |
| `url` | `string` | 跳转URL | ❌ | ✅ |

---

## 5. 换量任务完成上报 (finishExchangeTask) - 新接口

**接口名称**: 换量任务完成上报 (福利中心专用)
**接口地址**: `POST /incentive/ting/welfare/finishThirdpartyTask/exchangeTask/ts-{timestamp}`

**未登录处理**: 当外部APP换量任务调起福利中心但用户未登录时，会跳转到 `iting://open?msg_type=408&source=exchange`，跳转成功后会自动关闭当前RN页面，避免用户返回时回到旧版积分中心。
**请求方式**: POST
**Content-Type**: application/json

**使用场景**: 福利中心换量任务专用接口，当用户从第三方APP通过deeplink拉起喜马拉雅APP时，使用此接口上报任务完成状态。

**新方案特点**:
1. **专用接口**: 专门为福利中心换量任务设计
2. **简化参数**: 只保留核心必要参数
3. **base64编码**: 将完整deeplink进行base64编码后上报
4. **减少复杂度**: 避免参数解析错误和兼容性问题

**请求参数**:
```json
{
  "channelId": 200183,
  "token": "__TOKEN__",
  "channelName": "keepchange",
  "msgType": "94",
  "task": "1",
  "isGrowthExchange": true,
  "deeplinkData": "aXRpbmc6Ly9vcGVuP21zZ190eXBlPTk0JmJ1bmRsZT1ybl9jcmVkaXRfY2VudGVyJmNpZD0yMDAxODMmY2hhbm5lbGlkPWtlZXBjaGFuZ2UmY2hhbm5lbE5hbWU9a2VlcGNoYW5nZSZ0b2tlbj1hYWJiY2NkZGVlZmZhYWRkYWFkZCZpc19ncm93dGhfZXhjaGFuZ2Vfd2VsZmFyZT10cnVl"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 | 获取方式 |
|--------|------|------|------|----------|
| `channelId` | `number` | ✅ | 渠道ID | 从deeplink的`cid`参数获取 |
| `token` | `string` | ✅ | 任务令牌 | 从deeplink的`token`参数获取 |
| `channelName` | `string` | ✅ | 渠道名称 | 从deeplink的`channelName`参数获取 |
| `msgType` | `string` | ✅ | 消息类型 | 从deeplink的`msg_type`参数获取 |
| `task` | `string` | ✅ | 任务标识 | 从deeplink的`task`参数获取 |
| `isGrowthExchange` | `boolean` | ✅ | 福利中心换量标识 | 从deeplink的`is_growth_exchange_welfare`参数获取 |
| `deeplinkData` | `string` | ✅ | 完整deeplink的base64编码 | 将原始deeplink进行base64编码 |

**deeplinkData编码说明**:

**重要**: 必须使用**原始的完整deeplink URL**进行base64编码，不能从解析后的参数重新拼装！

```javascript
// ✅ 正确方式：直接使用原始deeplink
const originalDeeplink = "iting://open?msg_type=94&bundle=rn_credit_center&cid=200183&channelid=keepchange&channelName=keepchange&token=aabbccddeeffaaddaadd&is_growth_exchange=true";

// base64编码
const deeplinkData = btoa(encodeURIComponent(originalDeeplink));

// ❌ 错误方式：从解析参数重新拼装（可能丢失参数或顺序不一致）
// const rebuiltDeeplink = buildDeeplinkUrl(parsedParams); // 不推荐
```

**业务场景说明**:
这是一个**独立的外部拉活流程**，与福利中心的每日任务列表**没有任何关联**：
```
快手APP → 完成任务 → 拉起喜马拉雅APP → 福利页监听deeplink → 上报完成 → 流程结束
```

**响应格式**:
```json
{
  "ret": 0,
  "msg": "success",
  "responseId": 19084788657608,
  "data": {
    "taskId": "1",
    "channelName": "keepchange",
    "channelId": 200183,
    "reward": 200,
    "status": "completed",
    "reportTime": 1699123456789,
    "deeplinkData": "aXRpbmc6Ly9vcGVuP21zZ190eXBlPTk0JmJ1bmRsZT1ybl9jcmVkaXRfY2VudGVyJmNpZD0yMDAxODMmY2hhbm5lbGlkPWtlZXBjaGFuZ2UmY2hhbm5lbE5hbWU9a2VlcGNoYW5nZSZ0b2tlbj1hYWJiY2NkZGVlZmZhYWRkYWFkZCZpc19ncm93dGhfZXhjaGFuZ2U9dHJ1ZQ=="
  }
}
```

**响应字段说明**:
| 字段名 | 类型 | 说明 | 前端处理 |
|--------|------|------|----------|
| `ret` | `number` | 状态码 (0-成功, 非0-失败) | ✅ **关注** - 用于判断请求成功/失败 |
| `msg` | `string` | 响应消息 | ✅ **关注** - 用于日志输出 |
| `responseId` | `number` | 响应ID | ❌ **仅日志** - 不做业务处理 |
| `data.taskId` | `string` | 任务ID | ❌ **仅日志** - 不做业务处理 |
| `data.channelName` | `string` | 渠道名称 | ❌ **仅日志** - 不做业务处理 |
| `data.channelId` | `number` | 渠道ID | ❌ **仅日志** - 不做业务处理 |
| `data.reward` | `number` | 奖励金币数量 | ❌ **仅日志** - 不做业务处理 |
| `data.status` | `string` | 任务状态 | ❌ **仅日志** - 不做业务处理 |
| `data.reportTime` | `number` | 上报时间戳 | ❌ **仅日志** - 不做业务处理 |
| `data.deeplinkData` | `string` | 原始deeplink的base64编码 | ❌ **仅日志** - 不做业务处理 |

**前端处理说明**:
- ✅ **关注字段**: 只有 `ret` 和 `msg` 用于业务逻辑判断
- ❌ **仅日志字段**: 其他字段仅用于日志输出，不做任何toast提示或业务处理
- 🚫 **无用户提示**: 成功或失败都不显示toast，只在控制台输出日志

**与旧接口的区别**:
| 特性 | 旧接口 (finishThirdpartyTask) | 新接口 (finishExchangeTask) |
|------|------------------------------|----------------------------|
| **使用场景** | 积分中心第三方任务 | 福利中心换量任务 |
| **参数数量** | 11个字段 | 6个字段 |
| **参数复杂度** | 需要解析多个字段 | 核心参数+base64编码 |
| **数据完整性** | 可能丢失参数 | 完整保留原始deeplink |
| **维护成本** | 需要维护字段映射 | 服务端自行解析 |

---

## 埋点上报规范

### 换量任务埋点事件

换量任务的埋点上报统一在 `DailyTaskItem` 组件中处理，确保与每日任务使用相同的埋点字段和逻辑。

#### 1. 任务曝光埋点 (事件ID: 67695)

**触发时机**: 换量任务在列表中曝光时

**埋点参数**:
```javascript
{
  taskId: "10001",           // 任务ID (从task.taskId获取)
  taskType: "EXCHANGE",      // 任务类型 (从task.type获取)
  positionNew: 3,            // 任务在列表中的位置 (从实际索引获取)
  coins: 100,                // 奖励金币数量
  title: "折红包领金币",      // 任务标题
  status: 0,                 // 任务状态 (0-未完成, 1-已完成, 2-已领取)
  rta: false,                // 是否为RTA任务
  channelName: "keepchange", // 渠道名称 (从schemaLink解析)
  source: "welfare_center"   // 来源标识
}
```

**代码示例**:
```javascript
// 在DailyTaskItem组件的useEffect中
useEffect(() => {
  if (isExchangeTask(item)) {
    xmlog.log(67695, {
      taskId: item.taskId?.toString() || '',
      taskType: item.type || 'EXCHANGE',
      positionNew: index + 1,
      coins: item.coins || 0,
      title: item.title || '',
      status: item.status || 0,
      rta: item.rta || false,
      channelName: extractChannelFromSchema(item.schemaLink),
      source: 'welfare_center'
    });
  }
}, [item, index]);
```

#### 2. 任务点击埋点 (事件ID: 67696)

**触发时机**: 用户点击换量任务按钮时

**埋点参数**:
```javascript
{
  taskId: "10001",           // 任务ID
  taskType: "EXCHANGE",      // 任务类型
  positionNew: 3,            // 任务在列表中的位置
  coins: 100,                // 奖励金币数量
  title: "折红包领金币",      // 任务标题
  status: 0,                 // 任务状态
  rta: false,                // 是否为RTA任务
  channelName: "keepchange", // 渠道名称
  source: "welfare_center",  // 来源标识
  action: "execute",         // 操作类型 (execute-执行任务, claim-领取奖励)
  buttonText: "去完成"       // 按钮文案
}
```

**代码示例**:
```javascript
// 在handleExchangeTask函数中
const handleExchangeTask = async () => {
  // 埋点上报
  xmlog.log(67696, {
    taskId: item.taskId?.toString() || '',
    taskType: item.type || 'EXCHANGE',
    positionNew: index + 1,
    coins: item.coins || 0,
    title: item.title || '',
    status: item.status || 0,
    rta: item.rta || false,
    channelName: extractChannelFromSchema(item.schemaLink),
    source: 'welfare_center',
    action: canExecuteExchangeTask(item) ? 'execute' : 'claim',
    buttonText: getExchangeTaskButtonText(item)
  });

  // 执行任务逻辑...
};
```

#### 3. 第三方APP拉起埋点 (事件ID: 49511)

**触发时机**: 第三方APP通过deeplink拉起喜马拉雅APP时

**埋点参数**:
```javascript
{
  channelId: 200183,         // 渠道ID
  channelName: "keepchange", // 渠道名称
  msgType: "94",             // 消息类型
  isGrowthExchange: true,    // 是否为增长换量
  token: "abc123",           // 任务令牌
  source: "external_app",    // 来源标识
  timestamp: 1699123456789   // 拉起时间戳
}
```

**代码示例**:
```javascript
// 在handleExchangeTaskReturn函数中
export const handleExchangeTaskReturn = async (params: Record<string, any>) => {
  // 埋点上报 - 第三方APP拉起
  xmlog.log(49511, {
    channelId: parseInt(params.cid?.toString() || '0'),
    channelName: params.channelName || params.channelid || 'unknown',
    msgType: params.msg_type || '94',
    isGrowthExchange: params.is_growth_exchange === 'true',
    token: params.token || '',
    source: 'external_app',
    timestamp: Date.now()
  });

  // 处理任务逻辑...
};
```

### 新版福利中心 vs 旧版任务中心埋点差异

#### 埋点事件ID对比

| 埋点类型 | 新版福利中心 | 旧版任务中心 | 说明 |
|---------|-------------|-------------|------|
| 任务曝光 | **67695** | **42772** | 事件ID完全不同 |
| 任务点击 | **67696** | **40143** | 事件ID完全不同 |
| 第三方拉起 | **49511** | 无 | 新版新增的埋点 |

#### 字段差异对比

| 字段名 | 新版福利中心 | 旧版任务中心 | 差异说明 |
|-------|-------------|-------------|----------|
| 任务位置 | `positionNew` | `position` | 字段名不同 |
| 任务类型 | `taskType` | `taskType` (可选) | 新版必填，旧版可选 |
| 奖励信息 | `coins` | `rewardName` | 字段名和格式不同 |
| 任务状态 | `status` | 无 | 新版新增字段 |
| RTA标识 | `rta` | 无 | 新版新增字段 |
| 渠道信息 | `channelName` | 无 | 新版新增字段 |
| 来源标识 | `source` | 无 | 新版新增字段 |
| 操作类型 | `action` | 无 | 新版新增字段 |
| 按钮文案 | `buttonText` | `Item` | 字段名不同 |

#### 新版福利中心 (当前实现)

**特点**:
- 统一使用 `DailyTaskItem` 组件处理埋点
- 换量任务与每日任务使用相同的埋点事件ID (67695/67696)
- 通过 `taskType` 字段区分任务类型
- 包含完整的任务上下文信息

**埋点字段**:
```javascript
{
  taskId: string,      // 任务ID
  taskType: string,    // 任务类型 (EXCHANGE/RED_PACKET_RAIN/MARKET)
  positionNew: number, // 列表位置
  coins: number,       // 奖励金币
  title: string,       // 任务标题
  status: number,      // 任务状态
  rta: boolean,        // RTA标识
  channelName: string, // 渠道名称
  source: string       // 来源标识
}
```

#### 旧版任务中心 (积分中心)

**特点**:
- 使用独立的埋点事件ID：**42772** (曝光) 和 **40143** (点击)
- 字段命名和结构与新版不同
- 缺少部分上下文信息

**旧版埋点事件**:

##### **任务曝光埋点 (42772)**
```javascript
xmlog.event(42772, 'slipPage', {
  taskId: "10001",           // 任务ID (字符串)
  taskTitle: "去看免费小说领现金", // 任务标题
  currPage: "任务中心",       // 当前页面
  // 可能包含的额外字段:
  adValueGrade: "high",      // 广告价值等级 (广告任务)
  xmRequestId: "uuid",       // 请求ID
  responseId: "123456",      // 响应ID
  taskType: "adVideoNew"     // 任务类型 (部分任务)
});
```

##### **任务点击埋点 (40143)**
```javascript
xmlog.click(40143, undefined, {
  taskId: "10001",           // 任务ID (字符串)
  taskTitle: "去看免费小说领现金", // 任务标题
  currPage: "任务中心",       // 当前页面
  Item: "去完成",            // 按钮文案
  rewardName: "最高5元",     // 奖励描述
  position: "3",             // 任务位置 (注意：使用position而不是positionNew)
  // 可能包含的额外字段:
  adValueGrade: "high",      // 广告价值等级
  responseId: "123456",      // 响应ID
  taskType: "adVideoNew",    // 任务类型
  style: "意图卡片"          // 样式类型
});
```

#### 换量任务在旧版任务中心的埋点实现

**如果换量任务需要在旧版任务中心中实现**，应该遵循旧版的埋点规范：

##### **换量任务曝光 (42772)**
```javascript
// 在换量任务组件的曝光时触发
xmlog.event(42772, 'slipPage', {
  taskId: task.taskId?.toString() || '',
  taskTitle: task.title || '',
  currPage: '任务中心',
  // 换量任务特有字段
  channelName: extractChannelFromSchema(task.schemaLink),
  taskType: 'exchange'
});
```

##### **换量任务点击 (40143)**
```javascript
// 在用户点击换量任务时触发
xmlog.click(40143, undefined, {
  taskId: task.taskId?.toString() || '',
  taskTitle: task.title || '',
  currPage: '任务中心',
  Item: getExchangeTaskButtonText(task),
  rewardName: `${task.coins}金币`,
  position: (index + 1).toString(),
  // 换量任务特有字段
  channelName: extractChannelFromSchema(task.schemaLink),
  taskType: 'exchange'
});
```

**旧版实现注意事项**:
1. 使用 `position` 而不是 `positionNew`
2. 奖励信息使用 `rewardName` 字符串格式
3. 缺少 `status`、`rta`、`source` 等新版字段
4. 需要手动添加 `channelName` 和 `taskType` 来标识换量任务

### 埋点实现注意事项

#### 1. 字段统一性
- **taskId**: 统一使用字符串类型
- **positionNew**: 统一使用1基索引 (第一个任务为1)
- **taskType**: 统一使用大写枚举值 (EXCHANGE/RED_PACKET_RAIN/MARKET)
- **source**: 统一使用下划线命名 (welfare_center/external_app)

#### 2. 渠道名称提取
```javascript
// 从schemaLink中提取渠道名称的工具函数
const extractChannelFromSchema = (schemaLink: string): string => {
  try {
    const url = new URL(schemaLink);
    return url.searchParams.get('channelName') ||
           url.searchParams.get('channelid') ||
           'unknown';
  } catch {
    return 'unknown';
  }
};
```

#### 3. 埋点时机控制
- **曝光埋点**: 使用 `useEffect` 确保只在组件挂载时触发一次
- **点击埋点**: 在用户实际点击时触发，避免重复上报
- **拉起埋点**: 在 `handleExchangeTaskReturn` 函数开始时立即触发

#### 4. 错误处理
```javascript
// 埋点上报错误处理
const safeLog = (eventId: number, params: Record<string, any>) => {
  try {
    xmlog.log(eventId, params);
  } catch (error) {
    console.error(`埋点上报失败 (${eventId}):`, error);
  }
};
```

---

## 数据结构定义

### 任务类型枚举 (TaskType)
```typescript
enum TaskType {
  RED_PACKET_RAIN = "RED_PACKET_RAIN",  // 红包雨任务
  MARKET = "MARKET",                    // 商城任务  
  EXCHANGE = "EXCHANGE"                 // 换量任务
}
```

### 换量任务状态枚举 (ExchangeTaskStatus)
```typescript
enum ExchangeTaskStatus {
  PENDING = 0,      // 未完成，可执行
  COMPLETED = 1,    // 已完成，可领取奖励
  CLAIMED = 2       // 已领取奖励
}
```

### 换量任务扩展信息 (ExtMap)
```typescript
interface ExchangeTaskExtMap {
  taskId: number;     // 换量任务ID
  rta: boolean;     // 是否为RTA类型任务
}
```

---

## 业务流程

### 换量任务完整流程

1. **任务展示**: 调用 `queryDailyTask` 获取包含换量任务的每日任务列表 (包含h5Link和schemaLink)
2. **用户点击**: 用户点击换量任务
3. **直接跳转**: 优先使用schemaLink拉起第三方APP，失败则使用h5Link
4. **第三方拉起**: 第三方APP完成任务后，通过deeplink拉起喜马拉雅APP
5. **上报完成**: 调用 `finishExchangeTask` (新接口) 向服务端上报任务完成
6. **同步状态**: 调用 `completeExchangeTask` 同步任务完成状态 (传递taskId)
7. **领取奖励**: 用户点击领取，调用 `rewardGoldCoin` 发放金币奖励 (传递taskId)

### Deeplink参数示例

**实际换量任务Deeplink格式**:
```
iting://open?msg_type=94&bundle=rn_credit_center&cid=200183&channelid=keepchange&channelName=keepchange&token=aabbccddeeffaaddaadd&is_growth_exchange_welfare=true
```

**参数解析说明**:
| 参数名 | 示例值 | 说明 |
|--------|--------|------|
| `msg_type` | `94` | 消息类型（外部跳转喜马拉雅福利页固定值） |
| `bundle` | `rn_credit_center` | 固定值（APP代码中处理） |
| `cid` | `200183` | 渠道ID（实际字段名） |
| `channelid` | `keepchange` | 渠道标识 |
| `channelName` | `keepchange` | 渠道名称 |
| `token` | `aabbccddeeffaaddaadd` | 任务令牌 |
| `is_growth_exchange` | `true` | 增长换量标识 |

**兼容的Deeplink格式**:
```
iting://open?msg_type=94&task=10001&srcChannel=exchange_task&channelName=kuaishou&channelId=1001&token=abc123
```

---

## 错误码规范

| 错误码 | 说明 | 适用接口 |
|--------|------|----------|
| 200 | 成功 | 所有接口 |
| 400 | 参数错误 | 所有接口 |
| 401 | 未授权 | 所有接口 |
| 403 | 禁止访问 | 所有接口 |
| 404 | 资源不存在 | 所有接口 |
| 500 | 服务器内部错误 | 所有接口 |
| 1001 | 任务不存在 | completeExchangeTask, rewardGoldCoin |
| 1002 | 任务已完成 | completeExchangeTask |
| 1003 | 奖励已领取 | rewardGoldCoin |
| 1004 | 签名验证失败 | finishThirdpartyTask |
| 1005 | 渠道不支持 | finishThirdpartyTask |

---

## Mock数据参考

Mock数据文件位置: `mock_data/`

- `daily_task_list.json` - 每日任务列表Mock数据 (包含换量任务扩展字段和跳转链接)
- `complete_task.json` - 换量任务完成Mock数据 (completeExchangeTask接口)
- `reward_gold_coin.json` - 金币奖励Mock数据 (rewardGoldCoin接口)
- `finish_thirdparty_task.json` - 第三方任务完成上报Mock数据 (finishThirdpartyTask接口)
- `finish_exchange_task.json` - 换量任务完成上报Mock数据 (finishExchangeTask接口，新接口)

---

## 接口调用时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant App as 喜马拉雅APP
    participant ThirdParty as 第三方APP
    participant Server as 服务端

    User->>App: 打开福利中心
    App->>Server: queryDailyTask (获取任务列表)
    Server-->>App: 返回包含换量任务的列表

    User->>App: 点击换量任务
    App->>ThirdParty: 跳转到第三方APP

    User->>ThirdParty: 完成任务
    ThirdParty->>App: deeplink拉起喜马拉雅

    App->>Server: finishExchangeTask (上报任务完成，新接口)
    Server-->>App: 确认上报成功

    App->>Server: completeExchangeTask (同步任务状态)
    Server-->>App: 任务状态更新

    User->>App: 点击领取奖励
    App->>Server: rewardGoldCoin (发放金币)
    Server-->>App: 奖励发放成功
```

---

## 注意事项

### finishThirdpartyTask接口特殊说明

1. **业务场景区分**:
   - **福利中心换量任务**: 使用简化版本，`nonce`和`sig`传空字符串
   - **积分中心第三方任务**: 使用完整版本，需要生成`nonce`和`sig`

2. **参数验证**:
   - 福利中心: 服务端不验证`nonce`和`sig`，专注于任务完成状态上报
   - 积分中心: 服务端严格验证`nonce`和`sig`，确保请求安全性

3. **渠道信息**:
   - 渠道ID和渠道名称直接从deeplink参数中获取，不需要本地映射
   - 支持动态渠道，便于扩展新的第三方合作伙伴

### finishExchangeTask接口特殊说明

1. **业务场景**:
   - **独立流程**: 这是外部拉活流程，与福利中心每日任务**无关联**
   - **无上下文**: 用户可能从未打开过喜马拉雅APP，直接被第三方APP拉起
   - **纯上报**: 只是向服务端上报任务完成，不涉及任何业务逻辑

2. **原始deeplink处理**:
   - **必须使用原始URL**: 不能从解析参数重新拼装，避免参数丢失或顺序问题
   - **直接base64编码**: 将完整的原始deeplink进行base64编码后上报
   - **避免重新拼装**: 重新拼装可能导致参数顺序不一致或漏掉参数

3. **前端响应处理**:
   - **只关注**: `ret` 和 `msg` 字段用于业务逻辑判断 (ret=0表示成功)
   - **仅日志**: 其他字段 (`data.taskId`, `data.reward` 等) 仅用于日志输出
   - **无用户提示**: 成功或失败都不显示toast，只在控制台输出日志
   - **静默上报**: 上报过程对用户完全透明，不影响用户体验

4. **与旧接口的处理差异**:
   - **旧接口**: 可能有toast提示和业务逻辑处理
   - **新接口**: 纯粹的数据上报，无任何用户交互

### 通用注意事项

3. **taskId字段**: `completeExchangeTask` 和 `rewardGoldCoin` 接口都需要传递 `taskId` 字段
   - `completeExchangeTask`: 用于标识要完成的换量任务
   - `rewardGoldCoin`: 用于关联奖励发放与具体任务
4. **时间戳**: 所有时间戳使用13位毫秒级时间戳
5. **错误处理**: 所有接口都需要返回统一的错误码和错误信息格式
6. **幂等性**: `completeExchangeTask` 和 `rewardGoldCoin` 接口需要支持幂等操作
7. **并发控制**: 奖励发放需要考虑并发情况，避免重复发放
8. **参数编码**: deeplink中的URL参数需要正确进行URL编码/解码

---

## 开发优先级

### 第一阶段 (P0 - 核心功能)
1. `queryDailyTask` - 任务列表查询 (扩展支持换量任务，包含跳转链接)
2. `finishExchangeTask` - 换量任务完成上报 (新接口，福利中心专用)

### 第二阶段 (P1 - 完整流程)
3. `completeExchangeTask` - 任务完成同步
4. `rewardGoldCoin` - 金币奖励发放

### 第三阶段 (P2 - 兼容性)
5. `finishThirdpartyTask` - 第三方任务完成上报 (积分中心使用，保持兼容)

### 第三阶段 (P2 - 优化完善)
- 错误处理优化
- 性能监控
- 数据统计

---

## 联调测试建议

1. **Mock环境**: 使用提供的Mock数据进行前端开发
2. **接口联调**: 按优先级逐个接口进行联调测试
3. **端到端测试**: 完整的换量任务流程测试
4. **异常场景**: 网络异常、参数错误等异常情况测试
5. **性能测试**: 高并发场景下的接口性能测试
