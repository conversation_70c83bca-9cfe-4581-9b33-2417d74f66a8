import React from "react";
import { View, Animated, Text, Image } from "react-native";
import { getStyles } from "./styles";
import { useAtomValue } from "jotai";
import rewardModalContentThemeAtom, { darkTheme, lightTheme } from "./theme";
import { preloadImages } from "utils/preloadImages";
import { themeAtom } from "atom/theme";
import ConfirmButton from "components/CoinCenter/common/ConfirmButton";

// 导出金币图片以便预加载
export const COINS_IMAGE = 'https://imagev2.xmcdn.com/storages/99ff-audiofreehighqps/8C/1E/GKwRIRwL05RWAAA0tAOXmR1U.png';

export const useLotteryImages = () => {
  const theme = useAtomValue(themeAtom);
  const PRELOAD_IMAGES = theme === 'dark' ? [
    darkTheme.popupImage,
    COINS_IMAGE,
  ] : [
    lightTheme.popupImage,
    COINS_IMAGE,
  ];

  return () => preloadImages(PRELOAD_IMAGES);
};

// 导出所有需要预加载的图片
export const PRELOAD_IMAGES = [
  COINS_IMAGE,
  darkTheme.popupImage,
  lightTheme.popupImage
];

interface RewardModalContentProps {
  coins: number;
  btnText?: string;
  title?: string;
  subTitle?: string;
  onPress: () => void;
  scaleAnim?: Animated.Value;
}

export default function RewardModalContent({
  coins,
  btnText = '确定',
  title = '恭喜获得',
  subTitle,
  scaleAnim,
  onPress
}: RewardModalContentProps) {
  const theme = useAtomValue(rewardModalContentThemeAtom);
  const styles = getStyles(theme);
  // const scaleAnim = useRef(new Animated.Value(0)).current;
  return (
    <View style={styles.contentContainer}>
      <Animated.View
        style={[
          styles.popupImageContainer,
          {
            transform: [
              { scale: scaleAnim || 1 }
            ]
          }
        ]}
      >
        {/* Image组件可以直接使用预加载资源 */}
        <Image
          source={{ uri: theme.popupImage }}
          style={styles.popupImage}
        />

        <View style={styles.rewardContainer}>
          <Text style={styles.congratsText}>{title}</Text>
          {subTitle && <Text style={styles.subTitleText}>{subTitle}</Text>}
          <Image
            source={{ uri: COINS_IMAGE }}
            style={styles.coinIcon}
          />
          <Text style={styles.coinsText}>+{coins}</Text>
        </View>

        <ConfirmButton
          text={btnText}
          onPress={onPress}
          style={styles.confirmButton}
        />
      </Animated.View>
    </View>
  );
}