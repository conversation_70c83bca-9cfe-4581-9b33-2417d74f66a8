import React, { FC } from 'react'
import { BetterImage } from "@xmly/rn-components";
import { View, Text, TouchableOpacity, Image } from 'react-native'
import { useAtomValue } from 'jotai'
import LinearGradient from 'react-native-linear-gradient'
import { getStyles } from '../styles'
import { DayType, ShareCoinsTaskInfo } from '../store'
import { ShareCoinsTaskDayStatus } from 'services/welfare/shareCoinsTask'
import { BlurView } from '@react-native-community/blur'
import shareCoinsThemeAtom from '../theme'
import ProgressCircle from '../ProgressCircle'
import { px } from 'utils/px'
import { themeAtom } from 'atom/theme'

const markIcon = 'https://imagev2.xmcdn.com/storages/dce5-audiofreehighqps/28/BF/GKwRIJIL-0waAAAD1QOvgabR.png'
const maskBg = 'https://imagev2.xmcdn.com/storages/9427-audiofreehighqps/A8/AA/GKwRIRwL-3RLAAFQwAOvoSr0.png'

const missIconDark = 'https://imagev2.xmcdn.com/storages/b914-audiofreehighqps/56/8F/GKwRIasMJZouAAAF-wPJpyQ1.png';
const missIconLight = 'https://imagev2.xmcdn.com/storages/0a68-audiofreehighqps/9A/4F/GAqhp50MJZovAAAGvQPJpySP.png';
interface ProgressWindowProp {
  taskFailed: boolean
  shareConinsTaskInfo: ShareCoinsTaskInfo
  onComplementClick: () => void
  onSignClick: () => void
}

const ProgressWindow: FC<ProgressWindowProp> = ({ taskFailed, shareConinsTaskInfo, onComplementClick, onSignClick }) => {
  const theme = useAtomValue(shareCoinsThemeAtom)
  const styles = getStyles(theme)
  const osTheme = useAtomValue(themeAtom)
  const parseText = (text: string) => {
    const parts = text.split(/(\{\{.*?\}\})/)
    return parts.map((part, index) => {
      if (part.match(/\{\{.*?\}\}/)) {
        const emphasizedText = part.replace(/\{\{|\}\}/g, '')
        return (
          <Text
            key={index}
            style={styles.bold}
          >
            {emphasizedText}
          </Text>
        )
      }
      return <Text key={index}>{part}</Text>
    })
  }

  const renderFailWindow = () => {
    if (taskFailed) {
      return (
        <>
          <BlurView blurType={theme.blurType} blurAmount={6} style={styles.mask} overlayColor={theme.blurColor} />
          {/* <BetterImage
            source={{ uri: maskBg}}
            
            // width={'100%'}
            // height={'100%'}
            style={styles.mask}
            blurRadius={100}
            resizeMode='cover'
          /> */}
          <View style={styles.failedContainer}>
            <Text style={styles.failedTitle}>连签任务未完成，下周继续</Text>
            <Text style={styles.failedSubTitle}>{parseText(shareConinsTaskInfo.subTitle)}</Text>
          </View>
        </>
      )
    }
    return null
  }

  const renderStepLine = (day: DayType, index: number, bgColor: string) => {
    const { dayInfo } = shareConinsTaskInfo;
    const dayCount = dayInfo.length;
    const { stepDotColor } = theme;
    const COMPLEMENT_RED_COLOR = 'rgba(255, 68, 68, 1)';

    // First step line (from center to right)
    if (index === 0) {
      const isNextDone = dayInfo[index + 1].status === ShareCoinsTaskDayStatus.DONE;
      const isCurrentComplement = day.status === ShareCoinsTaskDayStatus.COMPLEMENT;
      const lineIsActive = isNextDone && isCurrentComplement;
      const color = lineIsActive ? stepDotColor : bgColor;

      return (
        <LinearGradient
          start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }}
          colors={['transparent', 'transparent', color, color]}
          locations={[0, 0.5, 0.5, 1]}
          style={styles.stepLine}
        />
      );
    }

    // Last step line (from left to center)
    if (index === dayCount - 1) {
      const isPrevComplement = dayInfo[index - 1].status === ShareCoinsTaskDayStatus.COMPLEMENT;
      const color = isPrevComplement ? COMPLEMENT_RED_COLOR : bgColor;

      return (
        <LinearGradient
          start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }}
          colors={[color, color, 'transparent', 'transparent']}
          locations={[0, 0.5, 0.5, 1]}
          style={styles.stepLine}
        />
      );
    }

    // Middle step lines (full line)
    const isPrevComplement = dayInfo[index - 1]?.status === ShareCoinsTaskDayStatus.COMPLEMENT;
    const isNextDone = dayInfo[index + 1]?.status === ShareCoinsTaskDayStatus.DONE;

    // Solid line for simple cases
    if (day.status === ShareCoinsTaskDayStatus.UNFINISHED || day.status === ShareCoinsTaskDayStatus.DONE) {
      return <View style={[styles.stepLine, { backgroundColor: bgColor }]} />;
    }

    let colors: string[] = [];
    if (day.status === ShareCoinsTaskDayStatus.PENDING) {
      const leftColor = isPrevComplement ? bgColor : stepDotColor;
      colors = [leftColor, leftColor, bgColor, bgColor];
    } else if (day.status === ShareCoinsTaskDayStatus.COMPLEMENT) {
      const rightColor = isNextDone ? stepDotColor : bgColor;
      colors = [stepDotColor, stepDotColor, rightColor, rightColor];
    } else {
      return null;
    }

    return (
      <LinearGradient
        start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }}
        colors={colors}
        locations={[0, 0.5, 0.5, 1]}
        style={styles.stepLine}
      />
    );
  }

  return (
    <View style={styles.progressContainer}>
      {/* 失败状态 */}
      {/* {renderFailWindow()} */}
      <View style={styles.row}>
        {shareConinsTaskInfo.dayInfo.map((day: DayType, index: number) => {
          const bgColor = (
            day.status === ShareCoinsTaskDayStatus.UNFINISHED ||
            day.status === ShareCoinsTaskDayStatus.DONE
          ) ?
            theme.stepDotColor :
            'rgba(255, 68, 68, 1)'
          return (
            <TouchableOpacity style={styles.itemContainer}>
              <View
                key={index}
                style={styles.item}
              >
                {day.status === ShareCoinsTaskDayStatus.DONE && (
                  <View style={styles.circleChecked}>
                    <Image
                      source={{ uri: markIcon }}
                      style={{ width: px(30), height: px(20) }}
                    />
                  </View>
                )}
                {(day.status === ShareCoinsTaskDayStatus.COMPLEMENT || day.status === ShareCoinsTaskDayStatus.PENDING) && (
                  day.totalTimes === day.progress ? <View style={styles.circleChecked}>
                    <Image
                      source={{ uri: markIcon }}
                      style={{ width: px(30), height: px(20) }}
                    />
                  </View> :
                    <TouchableOpacity
                      onPress={() => {
                        if (day.totalTimes !== day.progress) {
                          if (day.status === ShareCoinsTaskDayStatus.COMPLEMENT) {
                            onComplementClick()
                          }
                          if (day.status === ShareCoinsTaskDayStatus.PENDING) {
                            onSignClick()
                          }
                        }
                      }}
                    >
                      {!!day.totalTimes && (
                        <ProgressCircle
                          segment={day.totalTimes}
                          progress={day.progress as number / day.totalTimes}
                          status={day.status}
                        />
                      )}
                    </TouchableOpacity>
                )}
                {day.status === ShareCoinsTaskDayStatus.UNFINISHED && (
                  index > shareConinsTaskInfo.dayOfWeek ?
                    <View style={styles.circleUnfinished}>
                      <Text style={styles.circleText}>{day.text}</Text>
                    </View> :
                    <View style={styles.circleMiss}>
                      <Image
                        source={{ uri: osTheme === 'dark' ? missIconDark : missIconLight }}
                        style={{ width: px(30), height: px(30) }}
                      />
                    </View>
                )}
              </View>
              <View style={styles.stepProgress}>
                <View style={[styles.stepDot, { backgroundColor: bgColor }]} />
                {renderStepLine(day, index, bgColor)}
              </View>
              <View style={styles.stepInfo}>
                <Text
                  style={[
                    styles.stepTime,
                    {
                      color:
                        (day.status === ShareCoinsTaskDayStatus.COMPLEMENT || day.status === ShareCoinsTaskDayStatus.PENDING) && day.progress !== day.totalTimes ? theme.highlightStepInfoColor : theme.stepInfoColor,
                    },
                  ]}
                >
                  {day.label}
                </Text>
              </View>
            </TouchableOpacity>
          )
        })}
      </View>
    </View>
  )
}

export default ProgressWindow
