import React, { useEffect } from 'react'
import { View, Text, Image } from 'react-native'
import { useAtomValue, useSetAtom } from 'jotai'
import watchAd from 'utils/watchAd'
import { Toast } from '@xmly/rn-sdk'
import { getStyles } from './styles'
import { shareConinsTaskAtom, writeShareConinsTaskAtom } from './store'
import { changeShareCoinsStatusAtom } from 'atom/shareCoinsModal'
import { FallbackReqType } from 'constants/ad'
import { LISTEN_TASK_POSITION, AD_SOURCE, RewardType, NonCoinRewardType } from 'constants/ad'
import { ShareCoinsTaskStatus } from 'services/welfare/shareCoinsTask'
import useRewardGoldCoin from 'hooks/useRewardGoldCoin'
import ModuleCard from '../common/ModuleCard'
import shareCoinsThemeAtom from './theme'
import ProgressWindow from './ProgressWindow'

const titleIcon = 'https://imagev2.xmcdn.com/storages/058c-audiofreehighqps/E6/77/GAqhqKwL-H70AAAD6gOt2pBS.png'

const ShareCoinsCollapse = () => {
  const theme = useAtomValue(shareCoinsThemeAtom)
  const shareConinsTaskInfo = useAtomValue(shareConinsTaskAtom)
  const fetchShareCoinsTask = useSetAtom(writeShareConinsTaskAtom)
  const styles = getStyles(theme)
  const rewardGoldCoin = useRewardGoldCoin()
  const isCollapse = useAtomValue(changeShareCoinsStatusAtom)


  const taskFailed = shareConinsTaskInfo.taskStatus === ShareCoinsTaskStatus.FAILED && !shareConinsTaskInfo.replenishSignInBtnText

  useEffect(() => {
    fetchShareCoinsTask()
    // receiveAward()
  }, [])

  /**
   * 观看广告并领取奖励金币
   *
   * 1. 调用 watchAd 拉起广告，等待用户观看完成。
   * 2. 广告观看成功后，调用 rewardGoldCoin 领取奖励金币。
   * 3. 如果领取成功，刷新任务数据，否则弹出失败提示。
   * 4. 捕获并处理所有异常，确保用户有失败提示。
   * @returns {Promise<void>}
   */
  const handleWatchAd = async () => {
    try {
      const res = await watchAd({
        sourceName: AD_SOURCE.SHARE_COINS,
        positionName: LISTEN_TASK_POSITION.positionName,
        slotId: LISTEN_TASK_POSITION.slotId,
        rewardType: RewardType.SHARE_COINS,
        coins: 0,
        rewardVideoStyle: 0,
      })
      if (res.success) {
        const result = await rewardGoldCoin({
          rewardType: RewardType.SHARE_COINS,
          sourceName: AD_SOURCE.SHARE_COINS,
          coins: 0, //  自己传
          adId: res.adId, //  广告给
          adResponseId: res.adResponseId, // 广告给
          encryptType: res.encryptType, // 广告给
          ecpm: res.ecpm, // 广告给
          fallbackReq: res.fallbackReq ?? FallbackReqType.NORMAL, //广告给
        })
        if (result?.success) {
          fetchShareCoinsTask()
        }
      }
    } catch (e) {
      Toast.info('获取奖励失败')
    }
  }

  const parseText = (text: string) => {
    const parts = text.split(/(\{\{.*?\}\})/)
    return parts.map((part, index) => {
      if (part.match(/\{\{.*?\}\}/)) {
        const emphasizedText = part.replace(/\{\{|\}\}/g, '')
        return (
          <Text
            key={index}
            style={styles.bold}
          >
            {emphasizedText}
          </Text>
        )
      }
      return <Text key={index}>{part}</Text>
    })
  }

  const handleComplement = async () => {
    try {
      const res = await watchAd({
        sourceName: AD_SOURCE.SHARE_COINS,
        positionName: LISTEN_TASK_POSITION.positionName,
        slotId: LISTEN_TASK_POSITION.slotId,
        rewardType: RewardType.SHARE_COINS,
        coins: 0,
        rewardVideoStyle: 0,
      })
      if (res.success) {
        const result = await rewardGoldCoin(
          {
            rewardType: NonCoinRewardType.SHARE_COINS,
            sourceName: AD_SOURCE.SHARE_COINS,
            coins: 0,
            adId: res.adId,
            adResponseId: res.adResponseId,
            encryptType: res.encryptType,
            ecpm: res.ecpm,
            fallbackReq: res.fallbackReq ?? FallbackReqType.NORMAL,
            isNoCoin: true,
          },
          true
        )
        if (result?.success) {
          fetchShareCoinsTask()
        } else {
          Toast.info('获取奖励失败')
        }
      }
    } catch (e) {
      Toast.info('获取奖励失败')
    }
  }

  const renderTitleIcon = () => (
    <Image
      source={{ uri: titleIcon }}
      style={{ width: 35, height: 35, position: 'absolute', top: -20, left: '40%' }}
    />
  )

  const renderHeaderTitle = () => {
    if (isCollapse) {
      if (taskFailed) {
        return (
          <View style={[styles.headerTitle, styles.foldedHeaderTitle]}>
            <Text style={[styles.titleBold, styles.foldedTitle]}>下周瓜分百亿金币大奖敬请期待</Text>
          </View>
        )
      }
      return (
        <View style={[styles.headerTitle, styles.foldedHeaderTitle]}>
          <View>
            <Text style={[styles.titleBold, styles.foldedTitle]}>瓜分百亿金币大奖</Text>
            <Text style={[styles.collapseSubTitle, { textAlign: 'left' }]}>{parseText(shareConinsTaskInfo.subTitle)}</Text>
          </View>
          <Text style={styles.nextText}>明日再来</Text>
        </View>
      )
    }

    if (!taskFailed) {
      return (
        <View style={[styles.headerTitle, { position: 'relative' }]}>
          {renderTitleIcon()}
          <Text style={styles.titleBold}>
            连续7天瓜分
            <Text style={styles.titleHighlight}>百亿金币</Text>
          </Text>
          <Text style={styles.subTitle}>{parseText(shareConinsTaskInfo.subTitle)}</Text>
        </View>
      )
    } else {
      return (
        <View style={[styles.headerTitle, { position: 'relative' }]}>
          {renderTitleIcon()}
          <Text style={styles.titleBold}>
            下周瓜分<Text style={styles.titleHighlight}>百亿金币</Text>大奖敬请期待
          </Text>
        </View>
      )
    }
  }
  if (!isCollapse) return null
  if (!shareConinsTaskInfo.success) return null

  return (
    <ModuleCard>
      <View style={[styles.container, { paddingTop: 0 }]}>
        {/* 顶部标题 */}
        {renderHeaderTitle()}
        {/* 进度条 */}
        <ProgressWindow
          taskFailed={taskFailed}
          shareConinsTaskInfo={shareConinsTaskInfo}
          onComplementClick={handleComplement}
          onSignClick={handleWatchAd}
        />
      </View>
    </ModuleCard>
  )
}

export default ShareCoinsCollapse
