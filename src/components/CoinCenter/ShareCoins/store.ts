import { atom } from 'jotai';
import { 
  queryCarveUpAward, 
  ShareCoinsTaskDayStatus, 
  ShareCoinsTaskStatus, 
  ShareCoinsModalInfo,
  AwardStatus,
} from 'services/welfare/shareCoinsTask';

export interface DayType { 
  label: string, 
  status: ShareCoinsTaskDayStatus, 
  text: string,
  totalTimes?: number,
  progress?: number,
}

export interface ShareCoinsTaskInfo {
  success: boolean,
  failCode: number,
  taskStatus: ShareCoinsTaskStatus,
  dayInfo: DayType[],
  buttonText: string,
  minButtonText: string,
  enableReward: boolean,
  totalTimes: number,
  alreadyTimes: number,
  replenishSignInBtnText: string,
  replenishSignInMinBtnText: string,
  replenishSignInTotalTimes: number,
  replenishSignInAlreadyTimes: number,
  subTitle: string,
  shareCoinsModalInfo?: ShareCoinsModalInfo,
  dayOfWeek: number,
  loading: boolean,
}

// 初始状态
const initialShareCoinsTaskInfo: ShareCoinsTaskInfo = {
  success: false,
  failCode: 200,
  taskStatus: 1,
  dayInfo: [
    { label: '周一', status: ShareCoinsTaskDayStatus.UNFINISHED, text: '看' },
    { label: '周二', status: ShareCoinsTaskDayStatus.UNFINISHED, text: '广' },
    { label: '周三', status: ShareCoinsTaskDayStatus.UNFINISHED, text: '告' },
    { label: '周四', status: ShareCoinsTaskDayStatus.UNFINISHED, text: '瓜' },
    { label: '周五', status: ShareCoinsTaskDayStatus.UNFINISHED, text: '分' },
    { label: '周六', status: ShareCoinsTaskDayStatus.UNFINISHED, text: '大' },
    { label: '周日', status: ShareCoinsTaskDayStatus.UNFINISHED, text: '奖' }
  ],
  dayOfWeek: 0,
  buttonText: '',
  minButtonText: '',
  enableReward: true,
  totalTimes: 0,
  alreadyTimes: 0,
  replenishSignInBtnText: '',
  replenishSignInMinBtnText: '',
  replenishSignInTotalTimes: 0,
  replenishSignInAlreadyTimes: 0,
  subTitle: '有1.5万人正在挑战，预估人均可领{{5000}}金币',
  shareCoinsModalInfo: undefined,
  loading: true,
};


// 创建状态原子
export const shareConinsTaskAtom = atom<ShareCoinsTaskInfo>(initialShareCoinsTaskInfo);

// 写入/更新任务状态的原子
export const writeShareConinsTaskAtom = atom(
  null,
  async (get, set) => {
    const task = get(shareConinsTaskAtom)
    try {
      const response = await queryCarveUpAward();
      // console.log('-----queryCarveUpAward----- response ---->>>>>>>>---->', response)
      // const response = { data };
      
      if(!response?.data?.success) {
        // console.log('-----queryCarveUpAward----- response false---->>>>>>>>---->', response)
        set(shareConinsTaskAtom, {...task, success: false, loading: false});
      } else {
        const { detail } = response?.data;
        // console.log('------queryCarveUpAward detail --------->', detail)
        detail.sort((a, b) => a.day - b.day);
        const newInfo = {...task}
        let dayInfo = newInfo.dayInfo
        // console.log('------queryCarveUpAward dayInfo --------->', dayInfo)
        dayInfo = dayInfo.map((item, index) => {
          // 任务进度完成 detail 字段先排序，然后根据进度更新状态
          if(detail[index]?.status === 1) {
            return {
              ...item,
              status: ShareCoinsTaskDayStatus.DONE,
            }
          } 

          if(index === response?.data?.dayOfWeek - 1) {
            return {
              ...item,
              status: ShareCoinsTaskDayStatus.PENDING,
              totalTimes: response?.data?.maxRewardTimes,
              progress: response?.data?.alreadyRewardTimes,
            }
          }
          
          return item;
        })
        // dayInfo = testDayInfo;
        if(response?.data?.replenishSignIn) {
          dayInfo[response?.data?.replenishSignIn?.replenishSignInDays - 1] = {
            ...dayInfo[response?.data?.replenishSignIn?.replenishSignInDays - 1],
            status: ShareCoinsTaskDayStatus.COMPLEMENT,
            totalTimes: response?.data?.replenishSignIn?.totalTimes,
            progress: response?.data?.replenishSignIn?.alreadyTimes,
          }
          newInfo.replenishSignInBtnText = response?.data?.replenishSignIn?.btnText
          newInfo.replenishSignInMinBtnText = response?.data?.replenishSignIn?.minBtnText
          newInfo.replenishSignInTotalTimes = response?.data?.replenishSignIn?.totalTimes
          newInfo.replenishSignInAlreadyTimes = response?.data?.replenishSignIn?.alreadyTimes
        } else {
          newInfo.replenishSignInBtnText = ''
          newInfo.replenishSignInMinBtnText = ''
          newInfo.replenishSignInTotalTimes = 0
          newInfo.replenishSignInAlreadyTimes = 0
        }
        newInfo.dayOfWeek = response?.data?.dayOfWeek - 1
        newInfo.buttonText = response?.data?.buttonText
        newInfo.minButtonText = response?.data?.minButtonText
        newInfo.enableReward = response?.data?.enableReward
        newInfo.taskStatus = response?.data?.taskStatus
        newInfo.alreadyTimes = response?.data?.alreadyRewardTimes
        newInfo.totalTimes = response?.data?.maxRewardTimes
        newInfo.success = response?.data?.success
        // newInfo.taskStatus = 1
        newInfo.subTitle = response?.data?.subTitle
        set(shareConinsTaskAtom, {...task, ...newInfo, dayInfo, shareCoinsModalInfo: response?.data?.shareCoinsModalInfo, loading: false})
      }
      
    } catch (error) {
      set(shareConinsTaskAtom, {...task, success: true, loading: false})
      console.error('Failed to update listen task:', error);
    }
  }
);
