import React, { useEffect, useState, useContext, useMemo } from "react";
import { View, Text, Image } from "react-native";
import { getStyles } from "./styles";
import { useAtomValue, useSetAtom } from "jotai";
import dailyTaskItemThemeAtom from "./theme";
import { DailyTaskItem as DailyTaskItemType, DailyTaskType, ExchangeTaskStatus } from "services/welfare";
import { formatSeconds } from "utils/time";
import { useCountdown } from "../hooks/useCountdown";
import watchAd from "utils/watchAd";
import { FallbackReqType, DAILY_TASK_TYPE_TO_AD_SOURCE, DAILY_TASK_TYPE_TO_REWARD_TYPE } from "constants/ad";
import { updateDailyTaskAtom } from "../atom";
import { AD_SOURCE, RewardType } from "constants/ad";
import useRewardGoldCoin from "hooks/useRewardGoldCoin";
import { ScrollAnalyticComp } from "@xmly/react-native-page-analytics";
import xmlog from "utilsV2/xmlog";
import TaskButton from "components/CoinCenter/common/TaskButton";
import { useGameLaunch } from "components/CoinCenter/RedPacketRain";
// 【换量任务功能】导入换量任务相关工具函数
import {
  isExchangeTask,                    // 判断是否为换量任务
  getExchangeTaskButtonText,         // 获取换量任务按钮文案
  isExchangeTaskButtonClickable,     // 判断换量任务按钮是否可点击
  getExchangeTaskButtonColorState    // 获取换量任务按钮颜色状态
} from "services/welfare/exchangeTask";
import {
  executeExchangeTask,               // 执行换量任务跳转
  canExecuteExchangeTask,            // 判断是否可执行换量任务
  canClaimExchangeTaskReward,        // 判断是否可领取换量任务奖励
  initExchangeTaskReturnListener     // 初始化换量任务返回监听
} from "utils/exchangeTaskHelper";
import { DarkModeContext } from '../../../../contextV2/darkModeContext';
import { useNavigation } from "@react-navigation/native";
import { RootNavigationProps } from "router/type";

interface DailyTaskItemProps {
  item: DailyTaskItemType;
  countdown: number;
  isFirst?: boolean;
  loading?: boolean;
  updatingPositions: string[];
  index: number;
  onRewardCoinFinish: (coin: number) => void;
}

export default function DailyTaskItem({
  item,
  countdown,
  isFirst,
  loading,
  index,
  updatingPositions,
  onRewardCoinFinish,
}: DailyTaskItemProps) {
  const { isDarkMode } = useContext(DarkModeContext)
  const theme = useAtomValue(dailyTaskItemThemeAtom);
  const styles = getStyles(theme);
  const key = `${item.positionId}${item.title}`;
  const isUpdating = updatingPositions.includes(key);
  const updateDailyTask = useSetAtom(updateDailyTaskAtom);
  const rewardGoldCoin = useRewardGoldCoin();
  const [isProcessing, setIsProcessing] = useState(false);
  const [btnText, setBtnText] = useState('');
  const navigation = useNavigation<RootNavigationProps>();

  // 使用统一的红包雨钩子
  const { preloadResources, launchGame } = useGameLaunch();

  useCountdown({
    key,
    positionId: item.positionId,
    initialSeconds: countdown,
  });

  async function handlePress() {
    // 【统一埋点】根据任务类型进行统一的点击上报
    if (isExchangeTask(item)) {
      // 换量任务需要根据状态确定action
      const action = canExecuteExchangeTask(item) ? '去完成' :
                    canClaimExchangeTaskReward(item) ? '领取' : '点击';
      clickReport(action);
    } else {
      clickReport();
    }

    if (isProcessing) return;

    try {
      setIsProcessing(true);

      // 【换量任务功能】处理换量任务点击逻辑
      if (isExchangeTask(item)) {
        await handleExchangeTask();
        return;
      }

      // 处理喝水任务类型 - 跳转到喝水任务页面
      if (item.type === DailyTaskType.DRINK_WATER) {
        // 跳转到喝水任务页面
        navigation.navigate('DrinkWater');
        setIsProcessing(false);
        return;
      }

      // 处理红包雨任务类型
      if (item.type === DailyTaskType.RED_PACKET_RAIN) {
        // 启动红包雨游戏，并传入回调函数更新任务
        await launchGame(item, AD_SOURCE.RED_PACKET_RAIN, () => {
          updateDailyTask(key);
        });
        setIsProcessing(false);
        return;
      }

      // 处理普通广告任务
      const res = await watchAd({
        positionName: item.positionName,
        slotId: item.positionId,
        extInfo: item.extMap,
        sourceName: DAILY_TASK_TYPE_TO_AD_SOURCE[item.type ?? ''] ?? AD_SOURCE.DAILY,
      });

      if (res.success) {
        const result = await rewardGoldCoin({
          extMap: item.extMap,
          rewardType: DAILY_TASK_TYPE_TO_REWARD_TYPE[item.type ?? ''] ?? RewardType.DAILY,
          coins: item.coins,
          sourceName: DAILY_TASK_TYPE_TO_AD_SOURCE[item.type ?? ''] ?? AD_SOURCE.DAILY,
          adId: res.adId,
          adResponseId: res.adResponseId,
          ecpm: res.ecpm,
          encryptType: res.encryptType,
          fallbackReq: res?.fallbackReq ?? FallbackReqType.NORMAL,
        }, true);

        if (result?.success) {
          updateDailyTask(key);
          onRewardCoinFinish(result.coins);
        }
      }
    } catch (error) {
      console.error('Failed to handle daily task:', error);
    } finally {
      setIsProcessing(false);
    }
  }

  // 【换量任务功能】处理换量任务点击逻辑
  // 根据任务状态执行不同操作：跳转任务或领取奖励
  async function handleExchangeTask() {
    try {
      if (canExecuteExchangeTask(item)) {
        // 执行换量任务跳转（埋点已在handlePress中统一处理）
        await executeExchangeTask(item, () => {
          updateDailyTask(key);
        });
      } else if (canClaimExchangeTaskReward(item)) {
        // 领取换量任务奖励（埋点已在handlePress中统一处理）
        await claimExchangeTaskReward();
      }
    } catch (error) {
      console.error('处理换量任务失败:', error);
    } finally {
      setIsProcessing(false);
    }
  }

  // 【换量任务功能】领取换量任务奖励
  // 调用金币奖励接口发放换量任务奖励，使用和每日任务相同的弹窗提示
  async function claimExchangeTaskReward() {
    try {
      const result = await rewardGoldCoin({
        extMap: item.extMap || '',
        rewardType: RewardType.EXCHANGE, // 【修改】换量任务使用专用的EXCHANGE奖励类型
        coins: item.coins,
        sourceName: AD_SOURCE.EXCHANGE, // 【修改】换量任务使用专用的EXCHANGE来源
        fallbackReq: FallbackReqType.NORMAL,
        taskId: item.taskId, // 【换量任务功能】传递任务ID
      }, true); // 【重要】传入true禁用默认toast，使用自定义弹窗

      if (result?.success) {
        updateDailyTask(key);
        // 【换量任务功能】调用和每日任务相同的奖励弹窗回调
        onRewardCoinFinish(result.coins);
      }
    } catch (error) {
      console.error('领取换量任务奖励失败:', error);
    }
  }

  // 【统一埋点】统一的任务曝光上报
  function onShow() {
    // 如果是红包雨任务类型，提前预加载资源
    if (item.type === DailyTaskType.RED_PACKET_RAIN) {
      preloadResources();
    }

    // 统一的曝光上报 - 包含所有必要字段
    const exposureParams = {
      currPage: 'welfareCenter',
      moduleTitle: '每日任务',
      taskTitle: item.title,
      taskId: isExchangeTask(item) ? (item.taskId?.toString() || '') : `${item.positionId}`, // 统一taskId字段
      taskType: isExchangeTask(item) ? 'exchange' : (item.type || 'daily'), // 统一taskType字段
      positionNew: `${index + 1}`, // 统一positionNew字段
      Item: btnText, // 保留原有字段
    };

    xmlog.event(67696, 'slipPage', exposureParams);
  }

  // 【统一埋点】统一的任务点击上报
  function clickReport(action?: string) {
    // 统一的点击上报 - 包含所有必要字段
    const clickParams = {
      currPage: 'welfareCenter',
      moduleTitle: '每日任务',
      taskTitle: item.title,
      taskId: isExchangeTask(item) ? (item.taskId?.toString() || '') : `${item.positionId}`, // 统一taskId字段
      taskType: isExchangeTask(item) ? 'exchange' : (item.type || 'daily'), // 统一taskType字段
      positionNew: `${index + 1}`, // 统一positionNew字段
      Item: btnText, // 保留原有字段
      ...(action && { action }), // 换量任务的action字段
    };

    xmlog.click(67695, isExchangeTask(item) ? 'ExchangeTaskItem' : 'DailyTaskItem', clickParams);
  }

  function renderButton() {
    // 【换量任务功能】换量任务不受倒计时影响
    if (!isExchangeTask(item) && countdown > 0) {
      return <Text style={styles.countdown}>{formatSeconds(countdown)}后领取</Text>;
    }

    const buttonDisabled = isUpdating || isProcessing;
    const isExchange = isExchangeTask(item);

    // 【换量任务功能】换量任务按钮状态处理
    if (isExchange) {
      const buttonText = getExchangeTaskButtonText(item);
      const isClickable = isExchangeTaskButtonClickable(item);
      const colorState = getExchangeTaskButtonColorState(item);

      // 【换量任务功能】使用 useMemo 确保样式在状态变化时正确更新
      const finalButtonStyle = useMemo(() => {
        // 基础样式 - 每次都创建新对象避免缓存问题
        const baseStyle = {
          backgroundColor: theme.buttonBgColor,
          borderRadius: 15,
          paddingHorizontal: 12,
          height: 28,
          alignItems: 'center' as const,
          justifyContent: 'center' as const,
        };

        if (colorState === 'success') {
          // 待领取状态：保持与未完成状态一致的红色背景
          return [{
            ...baseStyle,
            backgroundColor: theme.buttonBgColor, // 与未完成状态保持一致的红色
          }];
        } else if (colorState === 'disabled') {
          // 已领取状态：透明背景，灰色文案
          return [{
            ...baseStyle,
            backgroundColor: 'transparent', // 透明背景
            opacity: 1,
          }];
        } else {
          if (buttonDisabled || !isClickable) {
            return [{
              ...baseStyle,
              opacity: 0.5, // 加载状态
            }];
          }
          return [baseStyle]; // 默认红色样式
        }
      }, [item.taskId, item.status, colorState, buttonDisabled, isClickable, theme.buttonBgColor]);

      // 已领取状态的文字样式需要特殊处理，与倒计时文字样式完全一致
      const textStyle = colorState === 'disabled' ? { 
        color: theme.countdownColor,    // 与"逛商城赚金币"、"拆红包领金币"倒计时颜色一致
        fontSize: 12,                   // 与倒计时字体大小一致 (px(12))
        fontFamily: 'XmlyNumber',       // 与倒计时字体族一致
        fontWeight: 'normal' as const   // 与倒计时字体粗细一致（不使用bold）
      } : undefined; // 未完成和待领取状态使用默认样式

      return (
        <TaskButton
          text={buttonText}
          style={finalButtonStyle}
          textStyle={textStyle}
          disabled={buttonDisabled || !isClickable}
          onPress={handlePress}
        />
      );
    }

    // 普通任务按钮
    return (
      <TaskButton
        text={item?.btnText ?? '去领取'}
        style={[
          styles.taskButton,
          buttonDisabled && styles.loadingButton
        ]}
        disabled={buttonDisabled}
        onPress={handlePress}
      />
    );
  }

  // 【换量任务功能】根据任务类型和状态更新按钮文案
  useEffect(() => {
    if (isExchangeTask(item)) {
      setBtnText(getExchangeTaskButtonText(item));
    } else if (countdown > 0) {
      setBtnText(`${formatSeconds(countdown)}后领取`);
    } else {
      setBtnText(item.btnText ?? '去领取');
    }
  }, [countdown, item]);

  // 【换量任务功能】初始化换量任务返回监听器
  useEffect(() => {
    const removeListener = initExchangeTaskReturnListener();
    return removeListener;
  }, []);

  return (
    <ScrollAnalyticComp
      itemKey={`DailyTaskItem_${item.positionId}_${item.title}`}
      onShow={onShow}
    >
      <View style={[styles.taskItem, isFirst && styles.firstTaskItem]} key={`task_${item.taskId}_${item.status}`}>{/* 【换量任务功能】key包含status确保状态变化时组件重新渲染 */}
        {
          (item.darkIcon || item.icon) ? <Image style={styles.coinIcon} source={{ uri: isDarkMode ? item.darkIcon : item.icon }} />
            : null
        }
        <View style={styles.taskInfo}>
          <View style={styles.taskNameView}>
            <Text numberOfLines={1} style={styles.taskName}>{item.title}</Text>
            {
              item.maxAmount ? <View style={styles.taskSubTitle}>
                <Image style={styles.taskSubTitleCoin} source={{ uri: 'https://imagev2.xmcdn.com/storages/c00d-audiofreehighqps/3E/2E/GAqh1QQMFFVRAAAEvAO-jCTD.png' }} />
                <Text style={styles.taskSubTitleText}>最高+{item.maxAmount}</Text>
              </View> : null
            }

          </View>
          <Text style={styles.taskReward}>{item.subTitle}</Text>
        </View>
        {renderButton()}
      </View>
    </ScrollAnalyticComp>
  );
}

