import { Platform, StyleSheet } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  taskItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: px(17),
    // marginRight: px(8),
  },
  firstTaskItem: {
    marginTop: px(12),
  },
  taskInfo: {
    flex: 1,
    marginRight: px(6),
  },
  taskName: {
    fontSize: px(14),
    color: theme.taskNameColor,
    fontWeight: Platform.select({ ios: '500', android: 'bold' })
  },
  taskReward: {
    fontSize: px(12),
    color: theme.taskRewardColor,
    marginTop: px(4),
    fontFamily: 'XmlyNumber',
  },
  taskButton: {
    backgroundColor: theme.buttonBgColor,
    borderRadius: px(15),
    paddingHorizontal: px(12),
    height: px(28),
    alignItems: 'center',
    justifyContent: 'center',

    // borderWidth: 1,
    // borderColor: 'green',
    // borderStyle: 'dashed',
  },
  buttonText: {
    fontSize: px(11),
    color: theme.buttonTextColor,
  },
  countdown: {
    fontSize: px(12),
    color: theme.countdownColor,
    fontFamily: 'XmlyNumber',
  },
  loadingButton: {
    opacity: 0.5,
  },
  // 【换量任务功能】换量任务按钮样式
  successButton: {
    backgroundColor: '#4CAF50', // 绿色，表示可领取状态
  },
  disabledButton: {
    backgroundColor: '#CCCCCC', // 灰色，表示已领取状态
    opacity: 1, // 移除透明度，确保颜色明显
  },
  coinIcon: {
    height: px(32),
    width: px(32),
    marginRight: px(10),
    position:'relative',
    top:px(2),
  },
  taskSubTitle: {
    marginLeft: px(6),
    minWidth: px(48),
    height: px(16),
    borderRadius: px(31),
    backgroundColor: theme.taskSubTitleColor,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft:px(4),
    paddingRight:px(4),
  },
  taskSubTitleText: {
    fontFamily: 'XmlyNumber',
    fontWeight: '500',
    textAlign: 'center',
    color: '#E8A746',
    fontSize:px(11),
  },
  taskNameView:{
    flexDirection: 'row',
    alignItems: 'center',
    width: px(140),
  },
  taskSubTitleCoin: {
    width: px(12),
    height: px(12),
    marginRight:px(2),
  }
}); 