import { StyleSheet } from "react-native";
import { px } from "utils/px";
import { lightTheme } from "./theme";

const getStyles = (theme: typeof lightTheme) => StyleSheet.create({
    container: {
    flexDirection: 'row',
    alignItems: 'center',
    height: px(40),
    borderRadius: px(2),
    marginBottom: px(10),
    backgroundColor: theme == lightTheme ? 'rgba(255,255,255,0.2)' : '#291F1F',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    height: px(40),
  },
  icon: {
    width: px(16),
    height: px(16),
    marginRight: px(11),
    marginLeft: px(16),
  },
  title: {
    flex: 1,
    fontSize: px(12),
    color: theme == lightTheme ? '#240000' : '#FFFFFF',
    fontWeight: '400',
    alignItems: 'center',
    textAlignVertical: 'center',
    height: px(40),
    width: '100%',
    paddingVertical: px(12),
  },
  titleContainer: {
    flex: 1,
    fontSize: px(12),
    color: theme == lightTheme ? '#240000' : '#FFFFFF',
    fontWeight: '400',
    alignItems: 'center',
    textAlignVertical: 'center',
    paddingVertical: px(0),
  },
  webViewTitle: {
    // flex: 1,
    fontSize: px(12),
    color: theme == lightTheme ? '#240000' : '#FFFFFF',
    // fontWeight: '400',
    // borderWidth: 1,
    // borderColor: 'red',
    // borderStyle: 'dashed',
    // alignItems: 'center',
    // textAlignVertical: 'center',
    height: px(40),
    width: px(250),
    paddingTop: px(12),
    paddingBottom: px(12),
    // paddingVertical: px(12),
  },
  closeIcon: {
    // position: 'relative',
    // right: px(16),
    width: px(14),
    height: px(14),
    marginLeft: px(10),
    marginRight: px(16),
    alignSelf: 'center',
  },
});

export default getStyles;