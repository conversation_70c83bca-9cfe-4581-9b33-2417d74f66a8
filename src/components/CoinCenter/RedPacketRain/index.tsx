import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, Animated, Easing, TouchableWithoutFeedback, BackHandler, InteractionManager } from 'react-native';
import type { NativeEventSubscription } from 'react-native';
import { getStyles } from './styles';
import { useVideoResources } from 'hooks/useVideoResources';
import { videoSources } from './sources';
import Modal from './Modal';
import { GameState } from './utils';
import { useFixedPackets } from './hooks/useFixedPackets';
import { useGameState } from './hooks/useGameState';
import { useVideoRenderer } from './hooks/useVideoRenderer';
import { EndPacket } from './components/EndPacket';
import CloseIcon from 'componentsV2/common/CloseIcon';
import { useAtom, useAtomValue } from 'jotai';
import { showRedPacketRainAtom, redPacketRainPosition<PERSON>tom, redPacketRainTaskAtom, redPacketRainGameEndCallback<PERSON>tom } from 'atom/redPacketRain'; // 假设atom路径，需要根据实际情况调整
import { DailyTaskItem, RewardGoldCoinResult } from 'services/welfare';
import { FallbackReqType } from 'constants/ad';
import { RewardType } from 'constants/ad';
import { AD_SOURCE } from 'constants/ad';
import watchAd from 'utils/watchAd';
import { hapticSuccess } from 'utils/haptic';
import useRewardGoldCoin from 'hooks/useRewardGoldCoin';
import Packets from './components/Packets';
import log from 'utils/log';
import DelayedRender from 'components/common/DelayedRender';
import xmlog from 'utilsV2/xmlog';

// 导出预加载资源的hook，方便外部提前加载
export const useRedPacketVideos = () => {
  return useVideoResources(videoSources, { autoLoad: false });
};

// 导出统一的红包雨游戏启动hook
export { useGameLaunch } from './hooks/useGameLaunch';

interface Props {
  visible: boolean;
  onGameEnd?: (score: number) => void;
  onClose?: () => void;
}
const debug = false;

export default function RedPacketRain({ visible, onGameEnd = () => { }, onClose }: Props) {
  const styles = getStyles();
  // 订阅showRedPacketRainAtom
  const [showRedPacketRain, setShowRedPacketRain] = useAtom(showRedPacketRainAtom);
  const position = useAtomValue(redPacketRainPositionAtom);
  const [awardCoins, setAwardCoins] = useState(0);
  const [adAwardCoins, setAdAwardCoins] = useState(0);
  const [adRewardModalVisible, setAdRewardModalVisible] = useState(false);
  const reward = useRewardGoldCoin();
  const redPacketRainTask = useAtomValue(redPacketRainTaskAtom);
  const callback = useAtomValue(redPacketRainGameEndCallbackAtom);
  const packetsOpacity = useRef(new Animated.Value(1)).current;
  const [showEndPacket, setShowEndPacket] = useState(false);

  useEffect(() => {
    let subscription: NativeEventSubscription | null = null;
    if (visible) {
      subscription = BackHandler.addEventListener(
        'hardwareBackPress',
        () => {
          handleCloseGame();
          return true;
        }
      );
    } else {
      if (subscription) {
        (subscription as NativeEventSubscription)?.remove();
        subscription = null;
      }
    }
    return () => {
      if (subscription) {
        (subscription as NativeEventSubscription)?.remove();
        subscription = null;
      }
    };
  }, [visible]);

  // 使用useVideoResources hook加载视频资源
  const {
    paths: sources,
    resources,
    ready,
  } = useVideoResources(videoSources, {
    autoLoad: visible // 当组件可见时自动加载资源
  });

  // 资源加载状态变化
  useEffect(() => {
    // 资源加载状态变更时的逻辑可以在这里添加
  }, [ready]);

  // 使用自定义hooks管理游戏状态
  const {
    gameState,
    awardBgShow,
    modalVisible,
    handleCountVideoLoad,
    handleCountVideoComplete,
    handleAwardBgStartComplete,
    handleOpenRedPacket,
    resetGameState,
    handleCloseModal
  } = useGameState({
    visible,
    ready,
    onGameEnd
  });
  const {
    packets,
    score,
    clickedPositions,
    handlePacketPress,
    resetPackets
  } = useFixedPackets(gameState);
  const [endPacketScale] = useState(new Animated.Value(0));
  const [splitLineAnimatedValue] = useState(new Animated.Value(0));

  // 监听游戏状态变化，处理游戏结束时的淡出效果
  useEffect(() => {
    log('debug__gameState', gameState);
    if (gameState === GameState.END) {
      // 使用requestAnimationFrame和setTimeout来错开动画执行时机
      requestAnimationFrame(() => {
        // 先执行红包淡出动画
        Animated.timing(packetsOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start();

        // 延迟显示EndPacket
        setTimeout(() => {
          setShowEndPacket(true);
          // 重置并开始EndPacket的动画
          endPacketScale.setValue(0);
          requestAnimationFrame(() => {
            Animated.timing(endPacketScale, {
              toValue: 1,
              duration: 750,
              useNativeDriver: true
            }).start();
          });
        }, 500); // 等红包开始淡出后再显示EndPacket
      });
    } else {
      setShowEndPacket(false);
    }
  }, [gameState]);

  // 在gameState变为end时播放动画 - 移除这个effect，因为已经在上面的effect中处理了
  useEffect(() => {
    if (gameState === GameState.END) {
      // 触发游戏结束回调
      onGameEnd?.(score);
    }
  }, [gameState, score, onGameEnd]);

  // 组件卸载或不可见时重置状态
  useEffect(() => {
    if (!visible) {
      resetPackets();
      endPacketScale.setValue(0);
      splitLineAnimatedValue.setValue(0);
    }
  }, [visible, resetPackets]);

  // 使用视频渲染hook
  const {
    renderVideo,
    renderClickEffects,
    renderCountdown
  } = useVideoRenderer({
    resources,
    sources,
    styles,
    gameState,
    handleCountVideoLoad,
    handleCountVideoComplete,
    handleAwardBgStartComplete
  });

  const updateTask = useCallback(() => {
    callback && callback?.callback?.(score);
  }, [position, callback]);

  // 处理红包点击打开动画
  const handleEndPacketPress = useCallback(async () => {
    const result = await reward({
      rewardType: RewardType.RED_PACKET_RAIN,
      coins: (redPacketRainTask as DailyTaskItem)?.coins || 0,
      sourceName: position,
      fallbackReq: FallbackReqType.NORMAL,
      extMap: JSON.stringify({
        packetNum: score,
        ...JSON.parse((redPacketRainTask as DailyTaskItem)?.extMap || '{}')
      })
    }, true);
    const { coins, upgradeCoins } = result || {};
    if (coins > 0) {
      hapticSuccess();
      setAwardCoins(coins);
    }
    if (upgradeCoins > 0) {
      setAdAwardCoins(upgradeCoins);
    }
    // 执行红包上下部分的动画
    Animated.timing(splitLineAnimatedValue, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
      easing: Easing.bezier(0.33, 1, 0.68, 1)
    }).start();

    updateTask();

    // 处理红包打开后的逻辑
    handleOpenRedPacket();
  }, [splitLineAnimatedValue, score, position, handleOpenRedPacket]);

  // 监听showRedPacketRain状态变化
  useEffect(() => {
    if (!showRedPacketRain) {
      // 当状态从true变为false时，强制重置游戏状态
      resetGameState();
      resetPackets();
      endPacketScale.setValue(0);
      splitLineAnimatedValue.setValue(0);

      // 如果有onClose回调，调用它
      if (onClose) {
        onClose();
      }
    }
  }, [showRedPacketRain, resetGameState, resetPackets, endPacketScale, splitLineAnimatedValue, onClose]);

  // 确保每次组件重新变为可见时，强制重置状态
  useEffect(() => {
    if (visible) {
      resetGameState();
      resetPackets();
      endPacketScale.setValue(0);
      splitLineAnimatedValue.setValue(0);
    }
  }, [visible, resetGameState, resetPackets, endPacketScale, splitLineAnimatedValue]);

  function onCloseBtnPress() {
    xmlog.event(68279, 'dialogClick', { currPage: 'welfareCenter' });
    handleCloseGame();
  }

  const handleCloseGame = useCallback(() => {
    // 设置atom状态为false
    setShowRedPacketRain(false);

    // 立即重置游戏状态，不等待状态变化
    resetGameState();
    resetPackets();
    handleCloseModal();
    setAdRewardModalVisible(false);
    packetsOpacity.setValue(1);
    endPacketScale.setValue(0);
    splitLineAnimatedValue.setValue(0);

    if (onClose) {
      onClose();
    }
  }, [onClose, setShowRedPacketRain, resetGameState, resetPackets, endPacketScale, splitLineAnimatedValue, packetsOpacity]);

  useEffect(() => {
    if (visible) {
      xmlog.event(68278, 'dialogView', { currPage: 'welfareCenter' });
    }
  }, [visible]);

  async function watchAdReward() {
    const result = await watchAd({
      sourceName: AD_SOURCE.RED_PACKET_RAIN,
      coins: adAwardCoins,
      extInfo: JSON.stringify({
        ...JSON.parse((redPacketRainTask as DailyTaskItem)?.extMap || '{}'),
        packetNum: score
      })
    });
    if (result.success) {
      const { ecpm, encryptType, fallbackReq = FallbackReqType.NORMAL, adId, adResponseId } = result;
      // log('debug__watchAdReward', position);
      const awardResult: RewardGoldCoinResult = await reward({
        rewardType: RewardType.AD_RED_PACKET_RAIN,
        coins: adAwardCoins,
        sourceName: position,
        fallbackReq: fallbackReq,
        ecpm,
        encryptType,
        adId,
        adResponseId,
        extMap: JSON.stringify({
          ...JSON.parse((redPacketRainTask as DailyTaskItem)?.extMap || '{}'),
          packetNum: score
        })
      }, true);
      const { coins } = awardResult;
      if (coins > 0) {
        hapticSuccess();
        setAwardCoins(coins);
        handleCloseModal();
        setAdRewardModalVisible(true);
        updateTask();
      }
    }
  }

  if (!visible) return null;

  return (
    <View style={styles.container}>
      {/* 奖励开始背景视频和循环背景统一管理 - 实现无缝过渡 */}
      <View pointerEvents="none" style={[styles.video]}>
        {/* 奖励开始视频 - 在播放时可见，播放完成后不可见 */}
        {gameState === GameState.END ? (
          <DelayedRender delay={300}>
            {
              renderVideo('awardBgStart', {
                onComplete: handleAwardBgStartComplete,
                repeat: false,
                style: [styles.video, { opacity: awardBgShow ? 0 : 1 }]
              })
            }
          </DelayedRender>
        ) : null}
        {/* 循环背景 - 始终加载但根据状态调整可见性 */}
        {renderVideo('awardBg', {
          repeat: true,
          style: [styles.video, { opacity: awardBgShow ? 1 : 0 }]
        })}
      </View>

      {/* 关闭按钮 */}
      <TouchableWithoutFeedback onPress={onCloseBtnPress}>
        <View style={styles.closeButton}>
          <CloseIcon color="#fff" size={16} />
        </View>
      </TouchableWithoutFeedback>

      {/* 游戏内容层 */}
      <View style={styles.contentContainer}>
        {[GameState.PLAYING, GameState.END].includes(gameState) ? (
          <Packets style={{ opacity: packetsOpacity }} packets={packets} handlePacketPress={handlePacketPress} />
        ) : null}
        {showEndPacket ? (
          <View style={styles.endContainer}>
            <EndPacket
              endPacketScale={endPacketScale}
              splitLineAnimatedValue={splitLineAnimatedValue}
              onPress={handleEndPacketPress}
            />
          </View>
        ) : null}
      </View>

      {/* 倒计时视频 - 准备阶段和游戏阶段显示 */}
      {renderCountdown()}

      {/* 点击动画层 */}
      {renderClickEffects(clickedPositions)}

      <Modal
        visible={gameState === GameState.END && modalVisible && !adRewardModalVisible}
        coins={awardCoins}
        onClose={handleCloseGame}
        onPress={watchAdReward}
        type='withAd'
        upgradeCoins={adAwardCoins}
      />
      <Modal
        visible={gameState === GameState.END && adRewardModalVisible}
        coins={awardCoins}
        onClose={handleCloseGame}
        upgradeCoins={adAwardCoins}
      />
    </View>
  );
}
