import { useRef, useCallback } from 'react';
import { useSetAtom } from 'jotai';
import { showRedPacketRainAtom, redPacketRainTaskAtom, redPacketRainPositionAtom, redPacketRainGameEndCallbackAtom } from 'atom/redPacketRain';
import { useRedPacketVideos } from '../index';
import { AD_SOURCE } from 'constants/ad';
import type { DailyTaskItem } from 'services/welfare';
import type { TouchTaskInfo } from 'services/welfare/touch';

/**
 * 统一管理红包雨游戏的初始化、预加载和启动逻辑
 * 在DailyTaskItem和TouchTask中复用
 */
export const useGameLaunch = () => {
  // 红包雨资源管理
  const { load: loadRedPacketResources } = useRedPacketVideos();
  const resourcesLoadedRef = useRef(false);

  // 红包雨全局状态控制
  const setShowRedPacketRain = useSetAtom(showRedPacketRainAtom);
  const setRedPacketRainTask = useSetAtom(redPacketRainTaskAtom);
  const setRedPacketRainPosition = useSetAtom(redPacketRainPositionAtom);
  const setRedPacketRainCallback = useSetAtom(redPacketRainGameEndCallbackAtom);

  /**
   * 预加载红包雨游戏资源
   * 可在组件挂载时或任务项曝光时调用
   */
  const preloadResources = useCallback(async () => {
    if (!resourcesLoadedRef.current) {
      await loadRedPacketResources();
      resourcesLoadedRef.current = true;
    }
  }, [loadRedPacketResources]);

  /**
   * 启动红包雨游戏
   * @param task - 任务信息（DailyTaskItem或TouchTaskInfo）
   * @param position - 任务位置标识
   * @param callback - 游戏结束后的回调函数
   */
  const launchGame = useCallback(
    async (
      task: DailyTaskItem | TouchTaskInfo,
      position: AD_SOURCE = AD_SOURCE.RED_PACKET_RAIN,
      callback?: (score: number) => Promise<void> | void
    ) => {
      // 确保资源已加载
      if (!resourcesLoadedRef.current) {
        await loadRedPacketResources();
        resourcesLoadedRef.current = true;
      }

      // 设置全局状态
      setRedPacketRainTask(task);
      setRedPacketRainPosition(position);
      
      // 如果有回调函数，设置游戏结束回调
      if (callback) {
        setRedPacketRainCallback({ callback });
      }

      // 显示红包雨游戏
      setShowRedPacketRain(true);
    },
    [
      loadRedPacketResources,
      setShowRedPacketRain,
      setRedPacketRainTask,
      setRedPacketRainPosition,
      setRedPacketRainCallback
    ]
  );

  /**
   * 检查资源是否已加载
   */
  const isResourcesLoaded = useCallback(() => {
    return resourcesLoadedRef.current;
  }, []);

  return {
    preloadResources,
    launchGame,
    isResourcesLoaded
  };
};
