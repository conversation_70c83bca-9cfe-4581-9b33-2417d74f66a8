import { atom } from 'jotai'
import { fetchNotificationPopupConfig } from 'services/welfare/notificationPopup'
import { NotificationPopupConfig } from './types'

// 通知弹窗配置
export const notificationPopupConfigAtom = atom<NotificationPopupConfig | null>(null)

// 通知弹窗显示状态
export const notificationPopupVisibleAtom = atom<boolean>(false)

// 通知弹窗是否已初始化
export const notificationPopupInitializedAtom = atom<boolean>(false)

// 通知弹窗优先级状态 - 用于控制是否应该显示
export const notificationPopupPriorityAtom = atom<boolean>(false)


// 写入/更新任务状态的原子
export const writeNotificationPopupConfigAtom = atom(
  null,
  async (get, set) => {
    const config = get(notificationPopupConfigAtom)
    try {
      const response = await fetchNotificationPopupConfig();
      console.log('NotificationPopupUI: 获取配置', response);
      if (response?.data && typeof response.data === 'object') {
        set(notificationPopupConfigAtom, response.data);
      } else {
        console.warn('Invalid notification popup config response:', response);
      }
    } catch (error) {
      console.error('Failed to fetch notification popup config:', error);
      // 保持当前配置不变
    }
  }
);